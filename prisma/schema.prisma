// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL") // uses connection pooling
}

model Address {
  id       String       @id @default(cuid())
  label    String
  name     String
  home     String
  userId   Int
  user     User         @relation(fields: [userId], references: [id])
  zoneId   String
  zone     DeliveryZone @relation(fields: [zoneId], references: [id])
  orders   Order[]
  phone    String
  location String?

  // @@unique([userId, zoneId])
  cart Cart[]
}

enum Role {
  USER
  ADMIN
  MAINTAINER
  SUPER_ADMIN
  DELIVERY_MAN
}

model User {
  id        Int       @id @default(autoincrement())
  createdAt DateTime  @default(now())
  email     String    @unique
  name      String
  avatar    String?
  phone     String?
  blocked   Boolean   @default(false)
  address   Address[]
  cart      Cart?

  role        Role        @default(USER)
  pickedItems OrderItem[]
  deliveries  Order[]     @relation(name: "DeliveryMan")
  orders      Order[]
  shop        Shop?

  notifications           Notification[]
  pushSubscriptions       PushSubscription[]
  summaries               Summary[]
  stockAdjustments        StockAdjustment[]
  productStockAdjustments ProductStockAdjustment[]
}

model Shop {
  id        String    @id @default(cuid())
  name      String
  createdAt DateTime  @default(now())
  slug      String    @unique
  location  String
  products  Product[]

  owner   User? @relation(fields: [ownerId], references: [id])
  ownerId Int?  @unique
}

model Category {
  id   String  @id @default(cuid())
  name String
  nam  String?

  titleEn String?
  titleBn String?
  titleBe String?

  descriptionEn String?
  descriptionBn String?

  slug           String     @unique
  isBase         Boolean    @default(false)
  hide           Boolean    @default(false)
  image          String?
  products       Product[]
  parentId       String?
  parentCategory Category?  @relation("Subcategories", fields: [parentId], references: [id], onDelete: Cascade)
  subCategories  Category[] @relation("Subcategories")
  position       Int        @default(100)
  featured       Boolean    @default(false)
}

model ProductImage {
  id        String  @id @default(cuid())
  url       String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId String
}

model Product {
  id          String         @id @default(cuid())
  slug        String?
  name        String
  nam         String?
  description String
  biboron     String?
  titleBe     String?
  images      ProductImage[]
  createdAt   DateTime       @default(now())
  updatedAt   DateTime?      @updatedAt

  position Int     @default(500)
  featured Boolean @default(false)

  amount   Int // Amount in terms of unit, like 500gm , 1ltr, 1kg, 1pcs
  supply   Int
  quantity Int @default(1) // Quantity per order like: 12 eggs, 1 container oil (5ltr)
  minOrder Int @default(1) // Minimum order quantity required
  maxOrder Int @default(10) // Maximum order quantity, null for unlimited

  price           Int
  discount        Int
  sourcePrice     Int
  hide            Boolean @default(false)
  alwaysAvailable Boolean @default(true) // If true, product is always available unless overridden by `availability` entries
  extraCharge     Int     @default(0)
  isHomeMade      Boolean @default(false) // If true, product is home-made and may take longer to prepare
  isHigherPriced  Boolean @default(false) // If true, product is sold at higher than regular price due to no commission

  // Stock management
  isOutsourced    Boolean @default(false) // If true, product has infinite stock (outsourced)

  shopId String?
  shop   Shop?   @relation(fields: [shopId], references: [id])

  categoryId String?
  category   Category? @relation(fields: [categoryId], references: [id])

  unitId String
  unit   QuantityUnit @relation(fields: [unitId], references: [id])

  cartItems  CartItem[]
  orderItems OrderItem[]

  company   Company? @relation(fields: [companyId], references: [id])
  companyId String?

  availability ProductAvailability[] // Link to specific availability schedules
  productStocks ProductStock[] // Link to stocks that supply this product
  stockAdjustments ProductStockAdjustment[] // Direct stock adjustments for this product
}

model Company {
  id       String    @id @default(cuid())
  name     String
  nam      String?
  slug     String    @unique
  products Product[]
}

model DeliveryZone {
  id         String         @id @default(cuid())
  name       String
  nam        String?
  slug       String         @unique
  charge     Int            @default(0) // Regular Delivery Charge
  express    Int            @default(0) // Express Delivery Change
  address    Address[]
  isBase     Boolean        @default(false)
  parentId   String?
  parentZone DeliveryZone?  @relation("Subzones", fields: [parentId], references: [id], onDelete: Cascade)
  subZones   DeliveryZone[] @relation("Subzones")
}

model QuantityUnit {
  id      String    @id @default(cuid())
  slug    String    @unique
  full    String
  bangla  String?
  product Product[]
  stocks  Stock[]
}

model Stock {
  id        String   @id @default(cuid())
  name      String   // e.g., "Rice Stock", "Cooking Oil Stock"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Current stock quantity in base unit (KG for weight, LTR for volume, PCS for count)
  currentQuantity Float @default(0)

  // Minimum stock level for alerts
  minimumLevel Float @default(0)

  // Unit for this stock (must be base unit: KG, LTR, or PCS)
  unitId String
  unit   QuantityUnit @relation(fields: [unitId], references: [id])

  // Products that use this stock
  products        ProductStock[]
  stockAdjustments StockAdjustment[]

  @@index([unitId])
}

model ProductStock {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  stockId String
  stock   Stock  @relation(fields: [stockId], references: [id], onDelete: Cascade)

  // How much stock (in base unit) is consumed per product unit
  // e.g., if product is "Rice 1KG" and stock is in KG, this would be 1.0
  // if product is "Rice 500GM" and stock is in KG, this would be 0.5
  consumptionRate Float

  @@unique([productId, stockId])
  @@index([productId])
  @@index([stockId])
}

// Simple stock adjustments for individual products (using supply field)
model ProductStockAdjustment {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Quantity change (positive for increase, negative for decrease)
  quantity Float

  // Reason for the adjustment
  reason StockAdjustmentReason

  // Optional note
  note String?

  // User who made the adjustment
  userId Int
  user   User @relation(fields: [userId], references: [id])

  @@index([productId])
  @@index([createdAt])
}

enum StockAdjustmentReason {
  SALE           // Stock sold to customer
  PURCHASE       // Stock purchased/received
  COUNT_ADJUSTMENT // Manual count adjustment
  DAMAGED        // Stock damaged/spoiled
  RETURNED       // Stock returned from customer
  EXPIRED        // Stock expired
  THEFT          // Stock lost due to theft
  TRANSFER_IN    // Stock transferred in from another location
  TRANSFER_OUT   // Stock transferred out to another location
  INITIAL        // Initial stock entry
}

model StockAdjustment {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  stockId String
  stock   Stock  @relation(fields: [stockId], references: [id], onDelete: Cascade)

  // Quantity change (positive for increase, negative for decrease)
  quantityChange Float

  // Quantity after this adjustment
  quantityAfter Float

  reason StockAdjustmentReason
  note   String?

  // Reference to order item if this adjustment is due to a sale
  orderItemId Int?
  orderItem   OrderItem? @relation(fields: [orderItemId], references: [id])

  // User who made this adjustment
  adjustedById Int
  adjustedBy   User @relation(fields: [adjustedById], references: [id])

  @@index([stockId])
  @@index([orderItemId])
  @@index([adjustedById])
  @@index([createdAt])
}

enum OrderStatus {
  PENDING
  CANCELLED
  CONFIRMED
  PACKED
  SHIPPING
  DELIVERED
  RETURNED
}

model Order {
  id        Int       @id @default(autoincrement())
  buyerId   Int
  buyer     User      @relation(fields: [buyerId], references: [id])
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  subTotal Int
  shipping Int
  discount Int
  status   OrderStatus @default(PENDING)
  profit   Int         @default(0)
  notes    String?

  addressId String
  address   Address @relation(fields: [addressId], references: [id])

  deliveryManId Int?
  deliveryMan   User? @relation(name: "DeliveryMan", fields: [deliveryManId], references: [id])

  orderItems OrderItem[]
  timeline   OrderTimeline[]
  summaryId  String?
  summary    Summary?        @relation(fields: [summaryId], references: [id])
}

model OrderTimeline {
  id        Int      @id @default(autoincrement())
  orderId   Int
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  status OrderStatus
  note   String?

  @@unique([orderId, status])
}

model OrderItem {
  id        Int      @id @default(autoincrement())
  orderId   Int
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  productId   String
  product     Product   @relation(fields: [productId], references: [id])
  quantity    Float
  price       Int
  sourcePrice Int       @default(0)
  picked      Boolean   @default(false)
  pickedAt    DateTime?
  pickerId    Int?
  picker      User?     @relation(fields: [pickerId], references: [id])

  // Stock management
  isOutsourced  Boolean @default(false) // Copied from product at order time

  stockAdjustments StockAdjustment[] // Stock adjustments related to this order item
}

model Cart {
  id        String    @id @default(cuid())
  userId    Int?      @unique
  user      User?     @relation(fields: [userId], references: [id])
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
  addressId String?
  address   Address?  @relation(fields: [addressId], references: [id])

  items CartItem[]
}

model CartItem {
  id        Int      @id @default(autoincrement())
  cartId    String
  cart      Cart     @relation(fields: [cartId], references: [id])
  createdAt DateTime @default(now())

  productId String
  product   Product @relation(fields: [productId], references: [id])
  quantity  Int

  @@unique([cartId, productId])
}

model Campaign {
  id String @id @default(cuid())

  title     String
  body      String
  imageUrl  String?
  targetUrl String?
  iconUrl   String?

  data         Json?
  scheduledFor DateTime @default(now())

  isSent Boolean   @default(false)
  sentAt DateTime?

  targetAudience Json?

  notifications Notification[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Notification {
  id         String    @id @default(cuid())
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId     Int
  campaign   Campaign? @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  campaignId String?

  title     String
  body      String
  imageUrl  String?
  targetUrl String?
  iconUrl   String?
  data      Json?

  isSeen    Boolean   @default(false)
  seenAt    DateTime?
  isClicked Boolean   @default(false)
  clickedAt DateTime?

  createdAt DateTime @default(now())

  @@unique([userId, campaignId])
}

model PushSubscription {
  id       String @id @default(cuid())
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId   Int
  endpoint String @unique
  p256dh   String
  auth     String

  createdAt DateTime @default(now())
}

enum DayOfWeek {
  SUNDAY
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
}

model ProductAvailability {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Type of availability schedule
  type AvailabilityType @default(DAILY_RECURRING)

  // For DAILY_RECURRING and WEEKLY_RECURRING
  dayOfWeek DayOfWeek? // Null for DAILY_RECURRING (applies every day)
  startTime String? // e.g., "09:00" (HH:MM format)
  endTime   String? // e.g., "17:00" (HH:MM format)

  // For DATE_RANGE and ONE_TIME
  startDate DateTime?
  endDate   DateTime?

  // If the entry makes the product available or unavailable
  isAvailable Boolean @default(true) // true for available, false for unavailable

  // Optional message to display when unavailable (for soft popup)
  beforeMessage String?
  afterMessage  String?

  // Priority for schedule ordering (higher number = higher priority)
  // Default priorities: DAILY_RECURRING=1, WEEKLY_RECURRING=2, DATE_RANGE=3, ONE_TIME=4
  priority Int @default(1)

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  // Ensures uniqueness for a product on a given day/time for daily/weekly schedules
  @@unique([productId, type, dayOfWeek, startTime, endTime], name: "product_time_unique_constraint")
  // Ensures uniqueness for a product within a date range for date-based schedules
  @@unique([productId, type, startDate, endDate], name: "product_date_unique_constraint")
}

enum AvailabilityType {
  DAILY_RECURRING // e.g., available every day from 9 AM to 5 PM
  WEEKLY_RECURRING // e.g., available only on Mondays and Wednesdays from 10 AM to 6 PM
  DATE_RANGE // e.g., available from Oct 1st to Oct 15th
  ONE_TIME // e.g., available on Dec 25th from 8 AM to 12 PM
}

enum SummaryType {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}

model Summary {
  id String @id @default(cuid())

  type      SummaryType
  startDate DateTime
  endDate   DateTime

  totalOrders          Int
  totalRevenue         Int
  totalDeliveryCharges Int
  totalProfit          Int

  notes         String?
  isFinalized   Boolean @default(false)
  generatedById Int
  generatedBy   User    @relation(fields: [generatedById], references: [id])

  createdAt DateTime @default(now())

  parentId       String?
  parentSummary  Summary?  @relation("SummaryHierarchy", fields: [parentId], references: [id])
  childSummaries Summary[] @relation("SummaryHierarchy")

  // --- RELATIONSHIP CHANGED FOR MANY-TO-MANY ---
  // A Summary can have many Orders through the SummaryOrder join table.
  orders Order[]

  @@unique([type, startDate])
}
