import { getPrisma } from "@udoy/utils/db-utils";
import { CartItemWithProduct, OrderItemWithProduct } from "@udoy/utils/types";

/**
 * Gets the most recent orders with customer information
 * @param limit Number of orders to return
 * @returns Array of recent orders with customer details
 */
export async function getRecentOrders(limit = 5) {
  const prisma = getPrisma();

  const orders = await prisma.order.findMany({
    take: limit,
    orderBy: {
      createdAt: "desc",
    },
    include: {
      buyer: true,
      orderItems: {
        include: {
          product: true,
        },
      },
    },
  });

  // Transform the data to match the component's expected format
  return orders.map((order) => {
    // Calculate total from order items
    const total = order.subTotal + order.shipping;

    return {
      id: order.id.toString(),
      customer: {
        name: order.buyer.name,
        email: order.buyer.email,
        avatar: order.buyer.avatar,
      },
      status: order.status,
      total: total,
      date: order.createdAt.toISOString(),
    };
  });
}

/**
 * Gets the count of orders for a specific time period
 * @param period Time period to count orders for
 * @returns Number of orders in the specified period
 */
export async function getOrdersCount(period: "today" | "week" | "month") {
  const prisma = getPrisma();

  let dateFilter: any = {};
  const now = new Date();

  if (period === "today") {
    // Start of today
    const startOfDay = new Date(now);
    startOfDay.setHours(0, 0, 0, 0);

    dateFilter = {
      gte: startOfDay,
    };
  } else if (period === "week") {
    // Start of this week (last 7 days)
    const startOfWeek = new Date(now);
    startOfWeek.setDate(startOfWeek.getDate() - 7);

    dateFilter = {
      gte: startOfWeek,
    };
  } else if (period === "month") {
    // Start of this month
    const startOfMonth = new Date(now);
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    dateFilter = {
      gte: startOfMonth,
    };
  }

  const count = await prisma.order.count({
    where: {
      createdAt: dateFilter,
    },
  });

  return count;
}

export function auditStock(cartItems: CartItemWithProduct[]) {
  let inStock = true;
  const stockChange = [] as { productId: string; decrement: number }[];

  for (const item of cartItems) {
    const supply = item.product.supply;
    const quantity = item.quantity;

    if (quantity > supply) {
      inStock = false;
      continue;
    }

    stockChange.push({
      productId: item.productId,
      decrement: quantity,
    });
  }

  return {
    inStock,
    stockChange,
  };
}

// New stock audit function for the new stock management system
export async function auditNewStock(cartItems: CartItemWithProduct[]) {
  const prisma = getPrisma();

  // Get all product-stock relationships for the cart items
  const productIds = cartItems.map(item => item.productId);
  const productStocks = await prisma.productStock.findMany({
    where: { productId: { in: productIds } },
    include: {
      product: { include: { unit: true } },
      stock: { include: { unit: true } },
    },
  });

  // Create order items with isOutsourced flag
  const orderItems = cartItems.map(item => ({
    productId: item.productId,
    quantity: item.quantity,
    price: item.product.price,
    sourcePrice: item.product.sourcePrice,
    isOutsourced: item.product.isOutsourced,
  }));

  // Use the new stock management utility to audit stock
  const { StockManagementUtil } = await import("@udoy/utils/stock-management");
  const auditResult = StockManagementUtil.auditStockForOrderItems(orderItems, productStocks);

  return {
    hasEnoughStock: auditResult.hasEnoughStock,
    insufficientItems: auditResult.insufficientItems,
    productStocks,
  };
}

// Deduct stock for order items
export async function deductStockForOrderItems(
  orderItems: Array<{ id: number; productId: string; quantity: number; isOutsourced: boolean }>,
  adjustedById: number,
  reason: import("@prisma/client").StockAdjustmentReason = "SALE" as any,
  note?: string
) {
  const prisma = getPrisma();

  // Get product-stock relationships for non-outsourced items
  const nonOutsourcedItems = orderItems.filter(item => !item.isOutsourced);
  if (nonOutsourcedItems.length === 0) {
    return { success: true, adjustments: [] };
  }

  const productIds = nonOutsourcedItems.map(item => item.productId);
  const productStocks = await prisma.productStock.findMany({
    where: { productId: { in: productIds } },
    include: {
      product: { include: { unit: true } },
      stock: { include: { unit: true } },
    },
  });

  const { StockManagementUtil } = await import("@udoy/utils/stock-management");

  // Group stock deductions by stock ID
  const stockDeductions = new Map<string, { totalDeduction: number; orderItemIds: number[] }>();

  for (const orderItem of nonOutsourcedItems) {
    const productStock = productStocks.find(ps => ps.productId === orderItem.productId);
    if (!productStock) {
      throw new Error(`No stock found for product ${orderItem.productId}`);
    }

    const deduction = StockManagementUtil.calculateOrderItemStockConsumption(orderItem, productStock);
    const existing = stockDeductions.get(productStock.stockId) || { totalDeduction: 0, orderItemIds: [] };

    stockDeductions.set(productStock.stockId, {
      totalDeduction: existing.totalDeduction + deduction,
      orderItemIds: [...existing.orderItemIds, orderItem.id],
    });
  }

  // Execute stock deductions in transaction
  const adjustments = await prisma.$transaction(async (tx) => {
    const createdAdjustments = [];

    for (const [stockId, { totalDeduction, orderItemIds }] of stockDeductions) {
      const stock = await tx.stock.findUnique({ where: { id: stockId } });
      if (!stock) {
        throw new Error(`Stock ${stockId} not found`);
      }

      const newQuantity = stock.currentQuantity - totalDeduction;

      // Update stock quantity
      await tx.stock.update({
        where: { id: stockId },
        data: { currentQuantity: newQuantity },
      });

      // Create stock adjustment record
      const adjustment = await tx.stockAdjustment.create({
        data: {
          stockId,
          quantityChange: -totalDeduction,
          quantityAfter: newQuantity,
          reason,
          note: note || `Stock deducted for order items: ${orderItemIds.join(', ')}`,
          adjustedById,
          orderItemId: orderItemIds[0], // Link to first order item for reference
        },
      });

      createdAdjustments.push(adjustment);
    }

    return createdAdjustments;
  });

  return { success: true, adjustments };
}
