import { QuantityUnit } from "@prisma/client";
import { localeNumber } from ".";
import { UnitCompatibilityUtil } from "./unit-compatibility";

export class UnitUtil {
  static getAmountUnit(
    am: number,
    unit: QuantityUnit,
    locale?: string
  ): string {
    const isBangla = locale === "bn";
    let unitLabel = isBangla
      ? unit?.bangla || unit.slug.toLowerCase()
      : unit.slug.toLowerCase();
    let amount = am;

    if (unit.slug === "GM") {
      if (amount >= 1000) {
        amount = amount / 1000;
        unitLabel = isBangla ? "কেজি" : "kg";
      } else {
        unitLabel = isBangla ? "গ্রাম" : "gm";
      }
    }

    if (unit.slug === "ML") {
      if (amount >= 1000) {
        amount = amount / 1000;
        unitLabel = isBangla ? "লিটার" : "ltr";
      } else {
        unitLabel = isBangla ? "মিলি" : "ml";
      }
    }

    if (unit.slug === "PCS") {
      unitLabel = isBangla ? "টি" : "pcs";
    }

    return `${amount.toLocaleString(locale)} ${unitLabel}`;
  }

  /**
   * Convert product amount to base unit for stock calculations
   */
  static convertToBaseUnit(amount: number, unit: QuantityUnit): number {
    return UnitCompatibilityUtil.convertToBaseUnit(amount, unit.slug);
  }

  /**
   * Check if two units are compatible for stock management
   */
  static areUnitsCompatible(unit1: QuantityUnit, unit2: QuantityUnit): boolean {
    return UnitCompatibilityUtil.areUnitsCompatible(unit1.slug, unit2.slug);
  }

  /**
   * Check if a unit can be used for stock (must be base unit)
   */
  static canBeUsedForStock(unit: QuantityUnit): boolean {
    return UnitCompatibilityUtil.validateStockUnit(unit.slug);
  }
}
