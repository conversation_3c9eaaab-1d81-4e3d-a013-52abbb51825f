import { Product, QuantityUnit, Stock, ProductStock, OrderItem, StockAdjustmentReason } from "@prisma/client";
import { UnitCompatibilityUtil } from "./unit-compatibility";

export type ProductWithUnit = Product & { unit: QuantityUnit };
export type StockWithUnit = Stock & { unit: QuantityUnit };
export type ProductStockWithRelations = ProductStock & {
  product: ProductWithUnit;
  stock: StockWithUnit;
};

export class StockManagementUtil {
  /**
   * Check if a product can be linked to a stock
   */
  static canLinkProductToStock(product: ProductWithUnit, stock: StockWithUnit): boolean {
    // Check if units are compatible
    if (!UnitCompatibilityUtil.areUnitsCompatible(product.unit.slug, stock.unit.slug)) {
      return false;
    }

    // Stock unit must be a base unit
    if (!UnitCompatibilityUtil.validateStockUnit(stock.unit.slug)) {
      return false;
    }

    return true;
  }

  /**
   * Calculate consumption rate for linking a product to a stock
   */
  static calculateProductStockConsumptionRate(
    product: ProductWithUnit,
    stock: StockWithUnit
  ): number {
    if (!this.canLinkProductToStock(product, stock)) {
      throw new Error(`Cannot link product ${product.name} to stock ${stock.name} - incompatible units`);
    }

    return UnitCompatibilityUtil.calculateConsumptionRate(
      product.amount,
      product.unit.slug,
      stock.unit.slug
    );
  }

  /**
   * Calculate total stock consumption for an order item
   */
  static calculateOrderItemStockConsumption(
    orderItem: OrderItem,
    productStock: ProductStockWithRelations
  ): number {
    return orderItem.quantity * productStock.consumptionRate;
  }

  /**
   * Check if there's enough stock for an order item
   */
  static hasEnoughStock(
    orderItem: OrderItem,
    productStock: ProductStockWithRelations
  ): boolean {
    // Skip stock check for outsourced products
    if (orderItem.isOutsourced) {
      return true;
    }

    const requiredStock = this.calculateOrderItemStockConsumption(orderItem, productStock);
    return productStock.stock.currentQuantity >= requiredStock;
  }

  /**
   * Check if there's enough stock for multiple order items
   */
  static auditStockForOrderItems(
    orderItems: OrderItem[],
    productStocks: ProductStockWithRelations[]
  ): {
    hasEnoughStock: boolean;
    insufficientItems: Array<{
      orderItem: OrderItem;
      productStock: ProductStockWithRelations;
      required: number;
      available: number;
    }>;
  } {
    const insufficientItems: Array<{
      orderItem: OrderItem;
      productStock: ProductStockWithRelations;
      required: number;
      available: number;
    }> = [];

    // Group order items by stock to handle multiple products using same stock
    const stockConsumption = new Map<string, number>();

    for (const orderItem of orderItems) {
      // Skip outsourced products
      if (orderItem.isOutsourced) {
        continue;
      }

      const productStock = productStocks.find(ps => ps.productId === orderItem.productId);
      if (!productStock) {
        // Product not linked to any stock - treat as insufficient
        insufficientItems.push({
          orderItem,
          productStock: null as any,
          required: orderItem.quantity,
          available: 0,
        });
        continue;
      }

      const required = this.calculateOrderItemStockConsumption(orderItem, productStock);
      const currentConsumption = stockConsumption.get(productStock.stockId) || 0;
      stockConsumption.set(productStock.stockId, currentConsumption + required);

      const totalRequired = stockConsumption.get(productStock.stockId)!;
      const available = productStock.stock.currentQuantity;

      if (totalRequired > available) {
        insufficientItems.push({
          orderItem,
          productStock,
          required,
          available: Math.max(0, available - (currentConsumption - required)),
        });
      }
    }

    return {
      hasEnoughStock: insufficientItems.length === 0,
      insufficientItems,
    };
  }

  /**
   * Get stock adjustment reason for order status changes
   */
  static getStockAdjustmentReasonForOrderStatus(
    orderStatus: string,
    isReturn: boolean = false
  ): StockAdjustmentReason | null {
    if (isReturn) {
      return StockAdjustmentReason.RETURNED;
    }

    switch (orderStatus) {
      case "DELIVERED":
        return StockAdjustmentReason.SALE;
      case "CANCELLED":
        return StockAdjustmentReason.RETURNED; // Return stock when order is cancelled
      default:
        return null;
    }
  }

  /**
   * Format stock quantity with appropriate unit
   */
  static formatStockQuantity(stock: StockWithUnit, locale?: string): string {
    const isBangla = locale === "bn";
    let unitLabel = isBangla ? stock.unit.bangla || stock.unit.slug : stock.unit.slug;
    
    // Convert to more readable units if needed
    if (stock.unit.slug === "KG" && stock.currentQuantity >= 1000) {
      const tons = stock.currentQuantity / 1000;
      unitLabel = isBangla ? "টন" : "ton";
      return `${tons.toLocaleString(locale)} ${unitLabel}`;
    }
    
    if (stock.unit.slug === "LTR" && stock.currentQuantity >= 1000) {
      const kiloliters = stock.currentQuantity / 1000;
      unitLabel = isBangla ? "কিলোলিটার" : "kl";
      return `${kiloliters.toLocaleString(locale)} ${unitLabel}`;
    }

    return `${stock.currentQuantity.toLocaleString(locale)} ${unitLabel}`;
  }

  /**
   * Check if stock is below minimum level
   */
  static isStockBelowMinimum(stock: Stock): boolean {
    return stock.currentQuantity <= stock.minimumLevel;
  }

  /**
   * Get stock status
   */
  static getStockStatus(stock: Stock): "OUT_OF_STOCK" | "LOW_STOCK" | "IN_STOCK" {
    if (stock.currentQuantity <= 0) {
      return "OUT_OF_STOCK";
    }
    
    if (this.isStockBelowMinimum(stock)) {
      return "LOW_STOCK";
    }
    
    return "IN_STOCK";
  }

  /**
   * Validate stock adjustment
   */
  static validateStockAdjustment(
    stock: Stock,
    quantityChange: number,
    reason: StockAdjustmentReason
  ): { isValid: boolean; error?: string } {
    const newQuantity = stock.currentQuantity + quantityChange;

    // Check for negative stock (except for certain reasons)
    if (newQuantity < 0) {
      const allowNegativeReasons = [
        StockAdjustmentReason.DAMAGED,
        StockAdjustmentReason.EXPIRED,
        StockAdjustmentReason.THEFT,
        StockAdjustmentReason.COUNT_ADJUSTMENT,
      ];

      if (!allowNegativeReasons.includes(reason)) {
        return {
          isValid: false,
          error: `Insufficient stock. Current: ${stock.currentQuantity}, Requested: ${Math.abs(quantityChange)}`,
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Calculate suggested consumption rate based on unit conversion
   */
  static calculateConsumptionRate(
    productUnit: string,
    stockUnit: string,
    productQuantity: number = 1
  ): number {
    // If units are the same, consumption rate is 1:1
    if (productUnit === stockUnit) {
      return productQuantity;
    }

    // Handle weight conversions
    if (this.WEIGHT_UNITS.includes(productUnit) && this.WEIGHT_UNITS.includes(stockUnit)) {
      const productInGrams = this.convertToBaseUnit(productQuantity, productUnit);
      const stockInGrams = this.convertToBaseUnit(1, stockUnit);
      return productInGrams / stockInGrams;
    }

    // Handle volume conversions
    if (this.VOLUME_UNITS.includes(productUnit) && this.VOLUME_UNITS.includes(stockUnit)) {
      const productInML = this.convertToBaseUnit(productQuantity, productUnit);
      const stockInML = this.convertToBaseUnit(1, stockUnit);
      return productInML / stockInML;
    }

    // Default to 1:1 for compatible units or same family
    return productQuantity;
  }
}
