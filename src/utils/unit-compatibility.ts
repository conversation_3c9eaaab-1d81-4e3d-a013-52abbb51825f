import { QuantityUnit } from "@prisma/client";

export enum UnitFamily {
  WEIGHT = "WEIGHT", // KG, GM
  VOLUME = "VOLUME", // LTR, ML
  COUNT = "COUNT",   // PCS
}

export interface UnitDefinition {
  slug: string;
  family: UnitFamily;
  baseUnitFactor: number; // How many base units this unit represents
  isBaseUnit: boolean;
}

// Unit definitions - this is the source of truth for unit compatibility
export const UNIT_DEFINITIONS: Record<string, UnitDefinition> = {
  // Weight units (base unit: KG)
  KG: {
    slug: "KG",
    family: UnitFamily.WEIGHT,
    baseUnitFactor: 1,
    isBaseUnit: true,
  },
  GM: {
    slug: "GM", 
    family: UnitFamily.WEIGHT,
    baseUnitFactor: 0.001, // 1 gram = 0.001 kg
    isBaseUnit: false,
  },
  
  // Volume units (base unit: LTR)
  LTR: {
    slug: "LTR",
    family: UnitFamily.VOLUME,
    baseUnitFactor: 1,
    isBaseUnit: true,
  },
  ML: {
    slug: "ML",
    family: UnitFamily.VOLUME,
    baseUnitFactor: 0.001, // 1 ml = 0.001 ltr
    isBaseUnit: false,
  },
  
  // Count units (base unit: PCS)
  PCS: {
    slug: "PCS",
    family: UnitFamily.COUNT,
    baseUnitFactor: 1,
    isBaseUnit: true,
  },
};

export class UnitCompatibilityUtil {
  /**
   * Get unit definition by slug
   */
  static getUnitDefinition(unitSlug: string): UnitDefinition | null {
    return UNIT_DEFINITIONS[unitSlug] || null;
  }

  /**
   * Get the unit family for a given unit
   */
  static getUnitFamily(unitSlug: string): UnitFamily | null {
    const definition = this.getUnitDefinition(unitSlug);
    return definition?.family || null;
  }

  /**
   * Check if two units are compatible (same family)
   */
  static areUnitsCompatible(unit1Slug: string, unit2Slug: string): boolean {
    const family1 = this.getUnitFamily(unit1Slug);
    const family2 = this.getUnitFamily(unit2Slug);
    
    return family1 !== null && family2 !== null && family1 === family2;
  }

  /**
   * Get the base unit for a given unit family
   */
  static getBaseUnitForFamily(family: UnitFamily): string {
    switch (family) {
      case UnitFamily.WEIGHT:
        return "KG";
      case UnitFamily.VOLUME:
        return "LTR";
      case UnitFamily.COUNT:
        return "PCS";
      default:
        throw new Error(`Unknown unit family: ${family}`);
    }
  }

  /**
   * Get the base unit for a given unit slug
   */
  static getBaseUnit(unitSlug: string): string | null {
    const family = this.getUnitFamily(unitSlug);
    return family ? this.getBaseUnitForFamily(family) : null;
  }

  /**
   * Check if a unit is a base unit
   */
  static isBaseUnit(unitSlug: string): boolean {
    const definition = this.getUnitDefinition(unitSlug);
    return definition?.isBaseUnit || false;
  }

  /**
   * Convert quantity from one unit to another (must be compatible)
   */
  static convertQuantity(
    quantity: number,
    fromUnitSlug: string,
    toUnitSlug: string
  ): number {
    if (!this.areUnitsCompatible(fromUnitSlug, toUnitSlug)) {
      throw new Error(`Cannot convert between incompatible units: ${fromUnitSlug} and ${toUnitSlug}`);
    }

    const fromUnit = this.getUnitDefinition(fromUnitSlug);
    const toUnit = this.getUnitDefinition(toUnitSlug);

    if (!fromUnit || !toUnit) {
      throw new Error(`Unknown unit: ${fromUnitSlug} or ${toUnitSlug}`);
    }

    // Convert to base unit first, then to target unit
    const baseQuantity = quantity * fromUnit.baseUnitFactor;
    const targetQuantity = baseQuantity / toUnit.baseUnitFactor;

    return targetQuantity;
  }

  /**
   * Convert quantity to base unit
   */
  static convertToBaseUnit(quantity: number, unitSlug: string): number {
    const baseUnitSlug = this.getBaseUnit(unitSlug);
    if (!baseUnitSlug) {
      throw new Error(`Cannot find base unit for: ${unitSlug}`);
    }
    
    return this.convertQuantity(quantity, unitSlug, baseUnitSlug);
  }

  /**
   * Convert quantity from base unit to target unit
   */
  static convertFromBaseUnit(quantity: number, targetUnitSlug: string): number {
    const baseUnitSlug = this.getBaseUnit(targetUnitSlug);
    if (!baseUnitSlug) {
      throw new Error(`Cannot find base unit for: ${targetUnitSlug}`);
    }
    
    return this.convertQuantity(quantity, baseUnitSlug, targetUnitSlug);
  }

  /**
   * Calculate consumption rate for a product-stock relationship
   * @param productAmount Product amount (e.g., 500 for "Rice 500GM")
   * @param productUnitSlug Product unit (e.g., "GM")
   * @param stockUnitSlug Stock unit (must be base unit, e.g., "KG")
   * @returns Consumption rate in stock units
   */
  static calculateConsumptionRate(
    productAmount: number,
    productUnitSlug: string,
    stockUnitSlug: string
  ): number {
    if (!this.isBaseUnit(stockUnitSlug)) {
      throw new Error(`Stock unit must be a base unit, got: ${stockUnitSlug}`);
    }

    if (!this.areUnitsCompatible(productUnitSlug, stockUnitSlug)) {
      throw new Error(`Product unit ${productUnitSlug} is not compatible with stock unit ${stockUnitSlug}`);
    }

    return this.convertQuantity(productAmount, productUnitSlug, stockUnitSlug);
  }

  /**
   * Get all units in a family
   */
  static getUnitsInFamily(family: UnitFamily): UnitDefinition[] {
    return Object.values(UNIT_DEFINITIONS).filter(unit => unit.family === family);
  }

  /**
   * Get all base units
   */
  static getBaseUnits(): UnitDefinition[] {
    return Object.values(UNIT_DEFINITIONS).filter(unit => unit.isBaseUnit);
  }

  /**
   * Validate that a unit can be used for stock (must be base unit)
   */
  static validateStockUnit(unitSlug: string): boolean {
    return this.isBaseUnit(unitSlug);
  }

  /**
   * Get human-readable unit family name
   */
  static getFamilyDisplayName(family: UnitFamily): string {
    switch (family) {
      case UnitFamily.WEIGHT:
        return "Weight";
      case UnitFamily.VOLUME:
        return "Volume";
      case UnitFamily.COUNT:
        return "Count";
      default:
        return "Unknown";
    }
  }
}
