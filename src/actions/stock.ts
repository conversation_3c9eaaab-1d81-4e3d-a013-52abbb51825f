"use server";

import { z } from "zod";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { getPrisma } from "@udoy/utils/db-utils";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-utils";
import { ActionError } from "@udoy/utils/app-error";
import { Role, StockAdjustmentReason } from "@prisma/client";
import { UnitCompatibilityUtil } from "@udoy/utils/unit-compatibility";
import { StockManagementUtil } from "@udoy/utils/stock-management";

// Schema for creating a new stock
const CreateStockSchema = z.object({
  name: z.string().min(1, "Stock name is required"),
  unitId: z.string().cuid("Invalid unit ID"),
  initialQuantity: z.coerce.number().min(0, "Initial quantity must be non-negative"),
  minimumLevel: z.coerce.number().min(0, "Minimum level must be non-negative"),
});

// Schema for stock adjustment
const StockAdjustmentSchema = z.object({
  stockId: z.string().cuid("Invalid stock ID"),
  quantityChange: z.coerce.number().refine(val => val !== 0, "Quantity change cannot be zero"),
  reason: z.nativeEnum(StockAdjustmentReason),
  note: z.string().optional(),
});

// Schema for linking product to stock
const ProductStockLinkSchema = z.object({
  productId: z.string().cuid("Invalid product ID"),
  stockId: z.string().cuid("Invalid stock ID"),
});

export type CreateStockInput = z.infer<typeof CreateStockSchema>;
export type StockAdjustmentInput = z.infer<typeof StockAdjustmentSchema>;
export type ProductStockLinkInput = z.infer<typeof ProductStockLinkSchema>;

/**
 * Create a new stock entry
 */
export async function createStock(data: CreateStockInput) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: { in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER] },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const { name, unitId, initialQuantity, minimumLevel } = CreateStockSchema.parse(data);

    // Validate that the unit can be used for stock (must be base unit)
    const unit = await prisma.quantityUnit.findUnique({ where: { id: unitId } });
    if (!unit) {
      return ActionError("Invalid unit");
    }

    if (!UnitCompatibilityUtil.validateStockUnit(unit.slug)) {
      return ActionError(`Unit ${unit.slug} cannot be used for stock. Only base units (KG, LTR, PCS) are allowed.`);
    }

    // Create stock and initial adjustment in transaction
    const result = await prisma.$transaction(async (tx) => {
      const stock = await tx.stock.create({
        data: {
          name,
          unitId,
          currentQuantity: initialQuantity,
          minimumLevel,
        },
        include: {
          unit: true,
        },
      });

      // Create initial stock adjustment if quantity > 0
      if (initialQuantity > 0) {
        await tx.stockAdjustment.create({
          data: {
            stockId: stock.id,
            quantityChange: initialQuantity,
            quantityAfter: initialQuantity,
            reason: StockAdjustmentReason.INITIAL,
            note: "Initial stock entry",
            adjustedById: userId,
          },
        });
      }

      return stock;
    });

    revalidatePath("/dashboard/stocks");
    return result;
  } catch (error: any) {
    console.error("Create stock error:", error);
    return ActionError(error?.message || "Failed to create stock");
  }
}

/**
 * Adjust stock quantity
 */
export async function adjustStock(data: StockAdjustmentInput) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: { in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER] },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const { stockId, quantityChange, reason, note } = StockAdjustmentSchema.parse(data);

    // Get current stock
    const stock = await prisma.stock.findUnique({
      where: { id: stockId },
      include: { unit: true },
    });

    if (!stock) {
      return ActionError("Stock not found");
    }

    // Validate the adjustment
    const validation = StockManagementUtil.validateStockAdjustment(stock, quantityChange, reason);
    if (!validation.isValid) {
      return ActionError(validation.error!);
    }

    const newQuantity = stock.currentQuantity + quantityChange;

    // Update stock and create adjustment record in transaction
    const result = await prisma.$transaction(async (tx) => {
      const updatedStock = await tx.stock.update({
        where: { id: stockId },
        data: { currentQuantity: newQuantity },
        include: { unit: true },
      });

      const adjustment = await tx.stockAdjustment.create({
        data: {
          stockId,
          quantityChange,
          quantityAfter: newQuantity,
          reason,
          note,
          adjustedById: userId,
        },
        include: {
          adjustedBy: { select: { name: true } },
        },
      });

      return { stock: updatedStock, adjustment };
    });

    revalidatePath("/dashboard/stocks");
    revalidatePath(`/dashboard/stocks/${stockId}`);
    return result;
  } catch (error: any) {
    console.error("Adjust stock error:", error);
    return ActionError(error?.message || "Failed to adjust stock");
  }
}

/**
 * Link a product to a stock
 */
export async function linkProductToStock(data: ProductStockLinkInput) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: { in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER] },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const { productId, stockId } = ProductStockLinkSchema.parse(data);

    // Get product and stock with units
    const [product, stock] = await Promise.all([
      prisma.product.findUnique({
        where: { id: productId },
        include: { unit: true },
      }),
      prisma.stock.findUnique({
        where: { id: stockId },
        include: { unit: true },
      }),
    ]);

    if (!product) {
      return ActionError("Product not found");
    }

    if (!stock) {
      return ActionError("Stock not found");
    }

    // Check if units are compatible
    if (!StockManagementUtil.canLinkProductToStock(product, stock)) {
      return ActionError(`Cannot link product ${product.name} to stock ${stock.name} - incompatible units`);
    }

    // Calculate consumption rate
    const consumptionRate = StockManagementUtil.calculateProductStockConsumptionRate(product, stock);

    // Create the link
    const productStock = await prisma.productStock.create({
      data: {
        productId,
        stockId,
        consumptionRate,
      },
      include: {
        product: { include: { unit: true } },
        stock: { include: { unit: true } },
      },
    });

    revalidatePath("/dashboard/stocks");
    revalidatePath(`/dashboard/stocks/${stockId}`);
    revalidatePath(`/dashboard/products/${productId}`);
    return productStock;
  } catch (error: any) {
    console.error("Link product to stock error:", error);
    return ActionError(error?.message || "Failed to link product to stock");
  }
}

/**
 * Unlink a product from a stock
 */
export async function unlinkProductFromStock(productStockId: string) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: { in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER] },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const productStock = await prisma.productStock.findUnique({
      where: { id: productStockId },
      include: {
        product: true,
        stock: true,
      },
    });

    if (!productStock) {
      return ActionError("Product-stock link not found");
    }

    await prisma.productStock.delete({
      where: { id: productStockId },
    });

    revalidatePath("/dashboard/stocks");
    revalidatePath(`/dashboard/stocks/${productStock.stockId}`);
    revalidatePath(`/dashboard/products/${productStock.productId}`);
    return { success: true };
  } catch (error: any) {
    console.error("Unlink product from stock error:", error);
    return ActionError(error?.message || "Failed to unlink product from stock");
  }
}
