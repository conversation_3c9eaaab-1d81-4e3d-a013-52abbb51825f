"use server";

import { OrderStatus, StockAdjustmentReason } from "@prisma/client";
import { sendPushNotification } from "@udoy/libs/backend/push-service";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";
import { deductStockForOrderItems } from "@udoy/libs/backend/orders";

export async function startDelivery(orderId: number) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const order = await prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      return ActionError("Order not found");
    }

    if (order.status !== OrderStatus.PACKED) {
      return ActionError("Order is not packed");
    }

    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: OrderStatus.SHIPPING,
        timeline: {
          create: {
            status: OrderStatus.SHIPPING,
            note: "Order Out for Delivery",
          },
        },
      },
    });

    await sendPushNotification(order.buyerId, {
      title: `অর্ডার ডেলিভারির জন্য বের হয়েছে। অর্ডার নংঃ ${order.id}`,
      body: `আপনার অর্ডারটি খুব দ্রুত আপনার ঠিকানায় পৌঁছে যাবে। আপনার অর্ডার নংঃ ${
        order.id
      }, টাকার পরিমাণ: ${(order.subTotal + order.shipping).toLocaleString(
        "bn"
      )} টাকা`,
      data: {
        url: `/orders`,
        tag: order.id,
      },
    });

    revalidatePath("/deliver");
    return true;
  } catch (error) {
    console.log(error);
    return ActionError(`Failed to start delivery`);
  }
}

export async function completeDelivery(orderId: number) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        orderItems: true,
      },
    });

    if (!order) {
      return ActionError("Order not found");
    }

    if (order.status !== OrderStatus.SHIPPING) {
      return ActionError("Order is not out for delivery");
    }

    // Check for unpicked items that need stock deduction
    const unpickedItems = order.orderItems.filter(item => !item.picked && !item.isOutsourced);

    if (unpickedItems.length > 0) {
      try {
        await deductStockForOrderItems(
          unpickedItems,
          userId,
          StockAdjustmentReason.SALE,
          `Stock deducted during delivery completion for order ${orderId}`
        );
      } catch (error) {
        console.error("Stock deduction failed during delivery:", error);
        return ActionError("Failed to deduct stock for unpicked items. Please check stock availability.");
      }
    }

    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: OrderStatus.DELIVERED,
        timeline: {
          create: {
            status: OrderStatus.DELIVERED,
            note: "Order Delivered",
          },
        },
      },
    });

    await sendPushNotification(order.buyerId, {
      title: `অর্ডার ডেলিভারি সম্পন্ন। অর্ডার নংঃ ${order.id}`,
      body: `আপনার অর্ডারটি সফলভাবে ডেলিভারি করা হয়েছে। আপনার অর্ডার নংঃ ${
        order.id
      }, টাকার পরিমাণ: ${(order.subTotal + order.shipping).toLocaleString(
        "bn"
      )} টাকা। উদয় মার্টের সাথে থাকার জন্য ধন্যবাদ!`,
      data: {
        url: `/orders`,
        tag: order.id,
      },
    });

    revalidatePath("/deliver");
    return true;
  } catch (error) {
    console.log(error);
    return ActionError(`Failed to complete delivery`);
  }
}
