"use server";

import { OrderStatus, StockAdjustmentReason } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";
import { deductStockForOrderItems } from "@udoy/libs/backend/orders";

export async function pickOrderItem(itemId: number, unpick?: boolean) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const orderItem = await prisma.orderItem.findUnique({
      where: { id: itemId },
      include: { order: true },
    });

    if (!orderItem) {
      return ActionError("Order item not found");
    }

    // If picking (not unpicking) and item is not outsourced, deduct stock
    if (!unpick && !orderItem.isOutsourced) {
      try {
        await deductStockForOrderItems(
          [orderItem],
          userId,
          StockAdjustmentReason.SALE,
          `Stock deducted during pickup for order ${orderItem.order.id}`
        );
      } catch (error) {
        console.error("Stock deduction failed:", error);
        return ActionError("Failed to deduct stock. Please check stock availability.");
      }
    }

    const updatedOrderItem = await prisma.orderItem.update({
      where: { id: itemId },
      data: {
        picked: unpick ? false : true,
        pickedAt: unpick ? null : new Date(),
        pickerId: unpick ? null : userId,
      },
      include: {
        order: true,
      },
    });

    revalidatePath(`/deliver/picking?orders=${updatedOrderItem.order.id}`);
    return true;
  } catch (error) {
    console.log(error);
    return ActionError(`Failed to ${unpick ? "unpick" : "pick"} order item`);
  }
}

export async function completePicking(orderId: number) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        orderItems: true,
      },
    });

    if (!order) {
      return ActionError("Order not found");
    }

    if (order.status !== "CONFIRMED") {
      return ActionError("Order is not confirmed");
    }

    const allPicked = order.orderItems.every((item) => item.picked);

    if (!allPicked) {
      return ActionError("All items are not picked");
    }

    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: OrderStatus.PACKED,
        timeline: {
          create: {
            status: OrderStatus.PACKED,
            note: "Order Packed and Ready for Delivery",
          },
        },
      },
    });

    revalidatePath("/deliver");
    return true;
  } catch (error) {
    console.log(error);
    return ActionError(`Failed to complete picking`);
  }
}
