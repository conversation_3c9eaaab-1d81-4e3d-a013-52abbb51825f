"use client";

import type React from "react";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  BarChart3,
  Package,
  ShoppingCart,
  Users,
  Star,
  Tag,
  Settings,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
  Home,
  StoreIcon,
  EditIcon,
  BuildingIcon,
  Bell,
  RefreshCw,
  FileText,
  Warehouse,
} from "lucide-react";

import { Button } from "@udoy/components/ui/button";
import { ScrollArea } from "@udoy/components/ui/scroll-area";
import { cn } from "@udoy/utils/shadcn";
import { useSelector } from "@xstate/store/react";
import { store } from "@udoy/state";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@udoy/components/ui/avatar";

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home,
  },
  // {
  //   title: "Analytics",
  //   href: "/dashboard/analytics",
  //   icon: BarChart3,
  // },
  {
    title: "Products",
    href: "/dashboard/products",
    icon: Package,
  },
  {
    title: "Stock Management",
    href: "/dashboard/stocks",
    icon: Warehouse,
  },
  {
    title: "Orders",
    href: "/dashboard/orders",
    icon: ShoppingCart,
  },
  {
    title: "Summaries",
    href: "/dashboard/summaries",
    icon: FileText,
  },
  {
    title: "Customers",
    href: "/dashboard/customers",
    icon: Users,
  },
  {
    title: "Reviews",
    href: "/dashboard/reviews",
    icon: Star,
  },
  {
    title: "Manage Items",
    href: "/dashboard/manage",
    icon: EditIcon,
  },
  {
    title: "Campaigns",
    href: "/dashboard/campaigns",
    icon: Bell,
  },
  {
    title: "Shops",
    href: "/dashboard/shops",
    icon: StoreIcon,
  },
  {
    title: "Companies",
    href: "/dashboard/company",
    icon: BuildingIcon,
  },

  {
    title: "Revalidate",
    href: "/dashboard/revalidate",
    icon: RefreshCw,
  },
  // {
  //   title: "Promotions",
  //   href: "/dashboard/promotions",
  //   icon: Tag,
  // },
  // {
  //   title: "Settings",
  //   href: "/dashboard/settings",
  //   icon: Settings,
  // },
  // {
  //   title: "Help",
  //   href: "/dashboard/help",
  //   icon: HelpCircle,
  // },
];

export function Sidebar({
  className,
  onClick,
}: {
  className?: string;
  onClick?: () => void;
}) {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);
  const me = useSelector(store, (state) => state.context.me);

  return (
    <div
      className={cn(
        "relative h-screen flex-col border-r bg-background transition-all duration-300 flex md:flex",
        collapsed ? "w-16" : "w-64",
        className
      )}
    >
      <div className="flex h-16 items-center justify-between border-b px-4">
        <Link
          href="/dashboard"
          className={cn(
            "flex items-center -mb-[3px]",
            collapsed && "justify-center"
          )}
        >
          {!collapsed && <span className="text-xl font-bold">Udoymart</span>}
          {collapsed && <span className="text-xl font-bold">U</span>}
        </Link>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setCollapsed(!collapsed)}
          className="h-8 w-8 hidden md:block"
        >
          {collapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
          <span className="sr-only">Toggle sidebar</span>
        </Button>
      </div>
      <ScrollArea className="flex-1 py-4">
        <nav className="grid gap-1 px-2">
          {navItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              onClick={onClick}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground",
                pathname === item.href
                  ? "bg-accent text-accent-foreground"
                  : "text-muted-foreground",
                collapsed && "justify-center px-2"
              )}
            >
              <item.icon className={cn("h-5 w-5", collapsed && "h-6 w-6")} />
              {!collapsed && <span>{item.title}</span>}
            </Link>
          ))}
        </nav>
      </ScrollArea>
      <div className="border-t p-4">
        <div
          className={cn(
            "flex items-center gap-3",
            collapsed && "justify-center"
          )}
        >
          <Avatar className="size-8 rounded-full">
            <AvatarImage src={me?.avatar!} alt={me?.name} />
            <AvatarFallback className="rounded-lg">CN</AvatarFallback>
          </Avatar>
          {!collapsed && (
            <div className="flex flex-col">
              <span className="text-sm font-medium">{me?.name}</span>
              <span className="text-xs text-muted-foreground">{me?.email}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
