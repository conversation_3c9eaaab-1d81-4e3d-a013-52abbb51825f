import { Suspense } from "react";
import { notFound } from "next/navigation";
import { getPrisma } from "@udoy/utils/db-utils";
import { Button } from "@udoy/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Badge } from "@udoy/components/ui/badge";
import {
  <PERSON><PERSON>,
  <PERSON>bsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import { Separator } from "@udoy/components/ui/separator";
import {
  ArrowLeft,
  Edit,
  Trash2,
  Package,
  DollarSign,
  ShoppingCart,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

async function getProductData(productId: string) {
  const prisma = getPrisma();

  const product = await prisma.product.findUnique({
    where: { id: productId },
    include: {
      images: true,
      unit: true,
      category: true,
      orderItems: true,
      shop: true,
      company: true,
      availability: true,
    },
  });

  if (!product) {
    return null;
  }

  // Calculate stats
  const totalSold =
    product.orderItems?.reduce((sum, item) => sum + item.quantity, 0) || 0;

  // Add stats to the product
  return {
    ...product,
    stats: {
      totalSold,
      averageRating: 4.5, // You can calculate this if you have ratings
      reviewCount: 0, // You can calculate this if you have reviews
      lastRestocked: product.createdAt,
    },
  };
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(date);
};

export default async function ProductDetailsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: productId } = await params;
  const product = await getProductData(productId);

  if (!product) {
    notFound();
  }

  // Get stock status
  const getStockStatus = (supply: number) => {
    if (supply === 0) {
      return {
        status: "out-of-stock",
        label: "Out of Stock",
        variant: "destructive" as const,
      };
    } else if (supply <= 10) {
      return {
        status: "low-stock",
        label: "Low Stock",
        variant: "secondary" as const,
      };
    } else {
      return {
        status: "in-stock",
        label: "In Stock",
        variant: "default" as const,
      };
    }
  };

  const stockStatus = getStockStatus(product.supply);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/dashboard/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">{product.name}</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="destructive">
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
          <Button asChild>
            <Link href={`/dashboard/products/${product.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Product
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Product images */}
        <Card className="md:col-span-1">
          <CardContent className="p-4">
            <div className="aspect-square overflow-hidden rounded-md">
              <Image
                width={400}
                height={400}
                src={product.images.at(0)?.url || "/placeholder.svg"}
                alt={product.name}
                className="h-full w-full object-cover"
              />
            </div>
            {product.images.length > 1 && (
              <div className="mt-4 grid grid-cols-4 gap-2">
                {product.images.map((image: any, index: number) => (
                  <div
                    key={image.id}
                    className={`aspect-square cursor-pointer overflow-hidden rounded-md border-2`}
                    // onClick={() => setActiveImageIndex(index)}
                  >
                    <Image
                      width={100}
                      height={100}
                      src={image.url || "/placeholder.svg"}
                      alt={`${product.name} ${index + 1}`}
                      className="h-full w-full object-cover"
                    />
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Product details */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Product Details</CardTitle>
            <CardDescription>
              Created on {formatDate(product.createdAt.toString())} • ID:{" "}
              {product.id}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Category
                </div>
                <div>{product.category?.name || "Uncategorized"}</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Stock Status
                </div>
                <div>
                  <Badge variant={stockStatus.variant as any}>
                    {stockStatus.label}
                  </Badge>
                  <span className="ml-2">{product.supply} in stock</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Price
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xl font-bold">৳{product.price}</span>
                  {product.discount > 0 && (
                    <span className="text-sm text-muted-foreground line-through">
                      ৳{product.price + product.discount}
                    </span>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Source Price
                </div>
                <div>৳{product.sourcePrice}</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Amount
                </div>
                <div>
                  {product.amount} {product.unit.full} ({product.unit.slug})
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Quantity per Order
                </div>
                <div>{product.quantity} units</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Max Order Limit
                </div>
                <div>{product.maxOrder} units</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Extra Charge
                </div>
                <div>৳{product.extraCharge}</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Total Sold
                </div>
                <div>{product.stats.totalSold} units</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Position
                </div>
                <div>{product.position}</div>
              </div>
              {/* <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Extra Charge
                </div>
                <div> ৳{product.extraCharge} </div>
              </div> */}
            </div>

            <Separator />

            <Tabs defaultValue="english">
              <TabsList>
                <TabsTrigger value="english">English</TabsTrigger>
                {product.nam && (
                  <TabsTrigger value="bengali">Bengali</TabsTrigger>
                )}
              </TabsList>
              <TabsContent value="english" className="pt-4">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">
                    Description
                  </div>
                  <div
                    dangerouslySetInnerHTML={{ __html: product.description }}
                  />
                </div>
              </TabsContent>
              {product.nam && (
                <TabsContent value="bengali" className="pt-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">
                      Name
                    </div>
                    <div>{product.nam}</div>
                  </div>
                  {product.biboron && (
                    <div className="mt-4 space-y-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Description
                      </div>
                      <div>{product.biboron}</div>
                    </div>
                  )}
                </TabsContent>
              )}
            </Tabs>

            <Separator />

            {/* Additional Product Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Additional Information</h3>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">
                    Product Names
                  </div>
                  <div className="space-y-1 text-sm">
                    <div><strong>English:</strong> {product.name}</div>
                    {product.nam && <div><strong>Bengali:</strong> {product.nam}</div>}
                    {product.titleBe && <div><strong>Bengali Title:</strong> {product.titleBe}</div>}
                    {product.slug && <div><strong>Slug:</strong> {product.slug}</div>}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">
                    Associations
                  </div>
                  <div className="space-y-1 text-sm">
                    {product.shop ? <div><strong>Shop:</strong> {product.shop.name}</div> : <div><strong>Shop:</strong> None</div>}
                    {product.company ? <div><strong>Company:</strong> {product.company.name}</div> : <div><strong>Company:</strong> None</div>}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">
                    Product Status
                  </div>
                  <div className="space-y-1 text-sm">
                    <div><strong>Featured:</strong> {product.featured ? "Yes" : "No"}</div>
                    <div><strong>Hidden:</strong> {product.hide ? "Yes" : "No"}</div>
                    <div><strong>Always Available:</strong> {product.alwaysAvailable ? "Yes" : "No"}</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">
                    Timestamps
                  </div>
                  <div className="space-y-1 text-sm">
                    <div><strong>Created:</strong> {formatDate(product.createdAt.toString())}</div>
                    {product.updatedAt && <div><strong>Updated:</strong> {formatDate(product.updatedAt.toString())}</div>}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Product stats */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Package className="mr-2" />
              <span className="mt-1">Inventory</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{product.supply}</div>
            <p className="text-xs text-muted-foreground">Units in stock</p>
            <div className="mt-4 space-y-2">
              <Button size="sm" className="w-full" asChild>
                <Link href={`/dashboard/products/${product.id}/stock`}>
                  Manage Stock
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <DollarSign className="mr-2" />
              <span className="mt-1">Revenue</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              ৳{product.stats.totalSold * product.price}
            </div>
            <p className="text-xs text-muted-foreground">
              Total revenue generated
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <ShoppingCart className="mr-2" />
              <span className="mt-1">Sales</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{product.stats.totalSold}</div>
            <p className="text-xs text-muted-foreground">Units sold</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
