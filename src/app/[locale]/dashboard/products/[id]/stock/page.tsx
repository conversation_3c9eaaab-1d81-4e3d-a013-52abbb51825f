import { notFound } from "next/navigation";
import { Button } from "@udoy/components/ui/button";
import { ArrowLeft, TrendingUp, Package, AlertTriangle, Plus } from "lucide-react";
import Link from "next/link";
import { getPrisma } from "@udoy/utils/db-utils";
import { Badge } from "@udoy/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { ProductStockManager } from "./components/ProductStockManager";

interface ProductStockPageProps {
  params: Promise<{ id: string }>;
}

export default async function ProductStockPage({ params }: ProductStockPageProps) {
  const { id } = await params;
  const prisma = getPrisma();

  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      unit: true,
      images: true,
      productStocks: {
        include: {
          stock: {
            include: {
              unit: true,
              stockAdjustments: {
                include: {
                  user: {
                    select: { name: true },
                  },
                },
                orderBy: {
                  createdAt: "desc",
                },
                take: 5,
              },
            },
          },
        },
      },
      // stockAdjustments: {
      //   orderBy: {
      //     createdAt: "desc",
      //   },
      //   take: 10,
      // },
    },
  });

  if (!product) {
    notFound();
  }

  // Get stock status
  const getStockStatus = (supply: number) => {
    if (supply === 0) {
      return {
        status: "out-of-stock",
        label: "Out of Stock",
        variant: "destructive" as const,
        icon: <AlertTriangle className="h-4 w-4" />,
      };
    } else if (supply <= 10) {
      return {
        status: "low-stock",
        label: "Low Stock",
        variant: "secondary" as const,
        icon: <AlertTriangle className="h-4 w-4" />,
      };
    } else {
      return {
        status: "in-stock",
        label: "In Stock",
        variant: "default" as const,
        icon: <Package className="h-4 w-4" />,
      };
    }
  };

  const stockStatus = getStockStatus(product.supply);

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/products/${product.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Product
          </Link>
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-3">
            {stockStatus.icon}
            <h1 className="text-3xl font-bold tracking-tight">Stock Management</h1>
            <Badge variant={stockStatus.variant}>{stockStatus.label}</Badge>
          </div>
          <p className="text-muted-foreground">
            Manage inventory for {product.name}
          </p>
        </div>
      </div>

      {/* Current Stock Overview */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Stock</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{product.supply}</div>
            <p className="text-xs text-muted-foreground">
              Units available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Product Unit</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{product.unit.slug}</div>
            <p className="text-xs text-muted-foreground">
              {product.unit.full}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">৳{(product.supply * product.sourcePrice).toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              At source price
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outsourced</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{product.isOutsourced ? "Yes" : "No"}</div>
            <p className="text-xs text-muted-foreground">
              {product.isOutsourced ? "Infinite stock" : "Managed stock"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Stock Management */}
      <ProductStockManager product={product} />

      {/* Linked Stocks (if any) */}
      {product.productStocks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Linked Stock Entries</CardTitle>
            <CardDescription>
              This product is linked to shared stock entries
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Stock Name</TableHead>
                  <TableHead>Stock Unit</TableHead>
                  <TableHead className="text-right">Current Stock</TableHead>
                  <TableHead className="text-right">Consumption Rate</TableHead>
                  <TableHead className="text-center">Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {product.productStocks.map((productStock) => {
                  const stock = productStock.stock;
                  const stockStatus = stock.currentQuantity <= stock.minimumLevel 
                    ? "Low Stock" 
                    : stock.currentQuantity === 0 
                    ? "Out of Stock" 
                    : "In Stock";
                  
                  return (
                    <TableRow key={productStock.id}>
                      <TableCell>
                        <div className="font-medium">{stock.name}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{stock.unit.slug}</Badge>
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {stock.currentQuantity.toFixed(2)} {stock.unit.slug}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {productStock.consumptionRate} {stock.unit.slug}
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge variant={
                          stockStatus === "Out of Stock" ? "destructive" :
                          stockStatus === "Low Stock" ? "secondary" : "default"
                        }>
                          {stockStatus}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Stock Adjustment History - Temporarily disabled */}
      {/*
      <Card>
        <CardHeader>
          <CardTitle>Stock Adjustment History</CardTitle>
          <CardDescription>
            Recent stock adjustments for this product
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <TrendingUp className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">History coming soon</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Stock adjustment history will be available soon
            </p>
          </div>
        </CardContent>
      </Card>
      */}
    </div>
  );
}
