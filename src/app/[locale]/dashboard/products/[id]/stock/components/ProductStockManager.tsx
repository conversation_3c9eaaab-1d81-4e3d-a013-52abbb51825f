"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Textarea } from "@udoy/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@udoy/components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Product, QuantityUnit, ProductStock, Stock } from "@prisma/client";
import { Plus, TrendingUp, Package, Settings } from "lucide-react";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import { Badge } from "@udoy/components/ui/badge";
import { adjustProductStock, toggleOutsourced } from "../actions";

const stockAdjustmentSchema = z.object({
  quantity: z.coerce.number().refine((val) => val !== 0, {
    message: "Quantity cannot be zero",
  }),
  reason: z.enum(["PURCHASE", "SALE", "DAMAGE", "RETURN", "COUNT_ADJUSTMENT", "INITIAL"], {
    required_error: "Please select a reason",
  }),
  note: z.string().optional(),
});

type StockAdjustmentValues = z.infer<typeof stockAdjustmentSchema>;

type ProductWithDetails = Product & {
  unit: QuantityUnit;
  productStocks: (ProductStock & {
    stock: Stock & {
      unit: QuantityUnit;
    };
  })[];
};

interface ProductStockManagerProps {
  product: ProductWithDetails;
}

const reasonLabels = {
  PURCHASE: "Purchase/Restock",
  SALE: "Sale/Consumption", 
  DAMAGE: "Damage/Loss",
  RETURN: "Return/Refund",
  COUNT_ADJUSTMENT: "Count Adjustment",
  INITIAL: "Initial Stock",
};

const reasonDescriptions = {
  PURCHASE: "Increase stock due to new purchases or restocking",
  SALE: "Decrease stock due to sales or consumption",
  DAMAGE: "Decrease stock due to damage, expiry, or loss", 
  RETURN: "Increase stock due to customer returns",
  COUNT_ADJUSTMENT: "Adjust stock based on physical count",
  INITIAL: "Initial stock entry",
};

export function ProductStockManager({ product }: ProductStockManagerProps) {
  const [isAdjustDialogOpen, setIsAdjustDialogOpen] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  const form = useForm<StockAdjustmentValues>({
    resolver: zodResolver(stockAdjustmentSchema),
    defaultValues: {
      quantity: 0,
      reason: "PURCHASE",
      note: "",
    },
  });

  const watchedQuantity = form.watch("quantity");
  const watchedReason = form.watch("reason");
  
  const newQuantity = product.supply + (watchedQuantity || 0);
  const isDecrease = (watchedQuantity || 0) < 0;
  const isIncrease = (watchedQuantity || 0) > 0;

  const onSubmit = async (data: StockAdjustmentValues) => {
    const result = await withError(() => 
      adjustProductStock({
        productId: product.id,
        quantity: data.quantity,
        reason: data.reason,
        note: data.note,
      })
    );
    
    if (result.success) {
      toast.success("Stock adjusted successfully");
      setIsAdjustDialogOpen(false);
      form.reset();
      // Refresh the page to show updated data
      window.location.reload();
    } else {
      toast.error(result.error);
    }
  };

  const handleToggleOutsourced = async () => {
    setIsToggling(true);
    const result = await withError(() => 
      toggleOutsourced(product.id)
    );
    
    if (result.success) {
      toast.success(`Product ${product.isOutsourced ? 'removed from' : 'marked as'} outsourced`);
      // Refresh the page to show updated data
      window.location.reload();
    } else {
      toast.error(result.error);
    }
    setIsToggling(false);
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Actions</CardTitle>
          <CardDescription>
            Quick actions for managing this product's inventory
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Dialog open={isAdjustDialogOpen} onOpenChange={setIsAdjustDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Adjust Stock
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Adjust Product Stock</DialogTitle>
                  <DialogDescription>
                    Modify stock levels for {product.name}
                  </DialogDescription>
                </DialogHeader>

                {/* Current Stock Display */}
                <div className="rounded-lg border p-4 bg-muted/50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Current Stock</h3>
                      <p className="text-2xl font-bold">{product.supply} {product.unit.slug}</p>
                    </div>
                    <div className="text-right">
                      <h3 className="font-medium">After Adjustment</h3>
                      <p className={`text-2xl font-bold ${
                        newQuantity < 0 ? 'text-red-600' : 
                        newQuantity <= 10 ? 'text-yellow-600' : 
                        'text-green-600'
                      }`}>
                        {newQuantity} {product.unit.slug}
                      </p>
                      {newQuantity < 0 && (
                        <Badge variant="destructive" className="mt-1">
                          Invalid: Below Zero
                        </Badge>
                      )}
                      {newQuantity >= 0 && newQuantity <= 10 && (
                        <Badge variant="secondary" className="mt-1">
                          Low Stock Warning
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="reason"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Adjustment Reason</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a reason" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.entries(reasonLabels).map(([value, label]) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            {reasonDescriptions[watchedReason as keyof typeof reasonDescriptions]}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="quantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Quantity Change</FormLabel>
                          <FormControl>
                            <div className="flex items-center gap-2">
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="Enter quantity (+ to add, - to subtract)"
                                {...field}
                                className={
                                  isDecrease ? 'border-red-300' : 
                                  isIncrease ? 'border-green-300' : ''
                                }
                              />
                              <span className="text-sm text-muted-foreground min-w-fit">
                                {product.unit.slug}
                              </span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            Use positive numbers to increase stock, negative numbers to decrease.
                            {isDecrease && (
                              <span className="text-red-600 block">
                                This will decrease stock by {Math.abs(watchedQuantity)} {product.unit.slug}
                              </span>
                            )}
                            {isIncrease && (
                              <span className="text-green-600 block">
                                This will increase stock by {watchedQuantity} {product.unit.slug}
                              </span>
                            )}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="note"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Note (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Add any additional notes..."
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <DialogFooter>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsAdjustDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={form.formState.isSubmitting || newQuantity < 0}
                      >
                        {form.formState.isSubmitting ? "Adjusting..." : "Adjust Stock"}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>

            <Button
              variant="outline"
              onClick={handleToggleOutsourced}
              disabled={isToggling}
            >
              <Settings className="mr-2 h-4 w-4" />
              {isToggling 
                ? "Updating..." 
                : product.isOutsourced 
                ? "Mark as In-House" 
                : "Mark as Outsourced"
              }
            </Button>
          </div>

          {product.isOutsourced && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Outsourced Product:</strong> This product has infinite stock and won't be tracked in inventory.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
