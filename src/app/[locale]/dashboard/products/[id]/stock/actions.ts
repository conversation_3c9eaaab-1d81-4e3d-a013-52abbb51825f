"use server";

import { getPrisma } from "@udoy/utils/db-utils";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import { ActionError } from "@udoy/utils/app-error";
import { revalidatePath } from "next/cache";

interface AdjustProductStockData {
  productId: string;
  quantity: number;
  reason: "PURCHASE" | "SALE" | "DAMAGE" | "RETURN" | "COUNT_ADJUSTMENT" | "INITIAL";
  note?: string;
}

export async function adjustProductStock(data: AdjustProductStockData) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const product = await prisma.product.findUnique({
      where: { id: data.productId },
    });

    if (!product) {
      return ActionError("Product not found");
    }

    // Check if product is outsourced
    if (product.isOutsourced) {
      return ActionError("Cannot adjust stock for outsourced products");
    }

    const newQuantity = product.supply + data.quantity;

    // Prevent negative stock for certain reasons
    if (newQuantity < 0) {
      const allowNegativeReasons = ["DAMAGE", "COUNT_ADJUSTMENT"];
      if (!allowNegativeReasons.includes(data.reason)) {
        return ActionError(
          `Insufficient stock. Current: ${product.supply}, Requested: ${Math.abs(data.quantity)}`
        );
      }
    }

    // Update product supply (temporarily without audit trail)
    await prisma.product.update({
      where: { id: data.productId },
      data: {
        supply: Math.max(0, newQuantity), // Ensure supply doesn't go below 0
      },
    });

    // TODO: Add audit trail back once schema is properly synced
    // await prisma.productStockAdjustment.create({
    //   data: {
    //     productId: data.productId,
    //     quantity: data.quantity,
    //     reason: data.reason as any,
    //     note: data.note,
    //     userId: parseInt(userId),
    //   },
    // });

    revalidatePath(`/dashboard/products/${data.productId}`);
    revalidatePath(`/dashboard/products/${data.productId}/stock`);
    revalidatePath("/dashboard/products");

    return { success: true };
  } catch (error) {
    console.error("Error adjusting product stock:", error);
    return ActionError("Failed to adjust stock");
  }
}

export async function toggleOutsourced(productId: string) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return ActionError("Product not found");
    }

    // Toggle the outsourced status
    await prisma.product.update({
      where: { id: productId },
      data: {
        isOutsourced: !product.isOutsourced,
      },
    });

    revalidatePath(`/dashboard/products/${productId}`);
    revalidatePath(`/dashboard/products/${productId}/stock`);
    revalidatePath("/dashboard/products");

    return { success: true };
  } catch (error) {
    console.error("Error toggling outsourced status:", error);
    return ActionError("Failed to update outsourced status");
  }
}

export async function createProductStock(productId: string, stockData: {
  name: string;
  initialQuantity: number;
  minimumLevel: number;
  consumptionRate: number;
}) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: { unit: true },
    });

    if (!product) {
      return ActionError("Product not found");
    }

    // Create a stock entry for this product
    const stock = await prisma.stock.create({
      data: {
        name: stockData.name,
        currentQuantity: stockData.initialQuantity,
        minimumLevel: stockData.minimumLevel,
        unitId: product.unit.id,
      },
    });

    // Link the product to the stock
    await prisma.productStock.create({
      data: {
        productId: productId,
        stockId: stock.id,
        consumptionRate: stockData.consumptionRate,
      },
    });

    // Create initial stock adjustment record
    await prisma.stockAdjustment.create({
      data: {
        stockId: stock.id,
        quantity: stockData.initialQuantity,
        reason: "INITIAL",
        note: "Initial stock creation",
        userId,
      },
    });

    revalidatePath(`/dashboard/products/${productId}`);
    revalidatePath(`/dashboard/products/${productId}/stock`);
    revalidatePath("/dashboard/stocks");

    return { success: true, stockId: stock.id };
  } catch (error) {
    console.error("Error creating product stock:", error);
    return ActionError("Failed to create stock entry");
  }
}
