"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { withError } from "@udoy/utils/app-error";
import { updateOrderItemQuantityByAmount } from "../actions";
import Locale from "@udoy/components/Locale/Client";

// Form validation schema
const amountFormSchema = z.object({
  targetAmount: z
    .number()
    .min(0.01, "Amount must be greater than 0")
    .max(100000, "Amount is too large"),
});

type AmountFormValues = z.infer<typeof amountFormSchema>;

interface AmountBasedQuantityModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderItem: {
    id: number;
    price: number;
    quantity: number;
    product: {
      name: string;
      nam?: string;
    };
  };
  orderId: number;
  onQuantityUpdated?: () => void;
}

// Helper function to format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat("bn-BD", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 0,
  }).format(amount);
}

export function AmountBasedQuantityModal({
  isOpen,
  onClose,
  orderItem,
  orderId,
  onQuantityUpdated,
}: AmountBasedQuantityModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<AmountFormValues>({
    resolver: zodResolver(amountFormSchema),
    defaultValues: {
      targetAmount: Math.round(orderItem.price * orderItem.quantity),
    },
  });

  const targetAmount = form.watch("targetAmount");
  const calculatedQuantity =
    targetAmount > 0 ? targetAmount / orderItem.price : 0;

  // Handle amount-based quantity update submission
  async function handleUpdateQuantity(values: AmountFormValues) {
    setIsLoading(true);
    try {
      const result = await withError(
        updateOrderItemQuantityByAmount({
          itemId: orderItem.id,
          orderId: orderId,
          targetAmount: values.targetAmount,
        })
      );
      if (result && typeof result === "object" && "success" in result) {
        toast.success(
          `Quantity updated to ${result.newQuantity} for ${formatCurrency(
            values.targetAmount
          )}`
        );
        onQuantityUpdated?.();
        onClose();
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to update quantity");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-sm">
        <DialogHeader>
          <DialogTitle>
            <Locale bn="পরিমাণ নির্ধারণ">Set Quantity</Locale>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Product Info */}
          <div className="text-center">
            <div className="font-medium">
              <Locale bn={orderItem.product.nam}>
                {orderItem.product.name}
              </Locale>
            </div>
            <div className="text-sm text-muted-foreground">
              <Locale bn="একক দাম">Unit Price</Locale>:{" "}
              {formatCurrency(orderItem.price)}
            </div>
          </div>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleUpdateQuantity)}
              className="space-y-4"
            >
              <FormField
                control={form.control}
                name="targetAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Locale bn="বিক্রয় মূল্য (৳)">Amount (৳)</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        placeholder="30"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                        disabled={isLoading}
                        className="text-center text-lg"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Simple Calculation Preview */}
              {targetAmount > 0 && (
                <div className="text-center text-sm text-muted-foreground">
                  <Locale bn="নতুন পরিমাণ">New Quantity</Locale>:{" "}
                  <span className="font-medium">
                    {calculatedQuantity.toFixed(2)}
                  </span>
                </div>
              )}

              <DialogFooter className="gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  <Locale bn="বাতিল">Cancel</Locale>
                </Button>
                <Button type="submit" disabled={isLoading || targetAmount <= 0}>
                  {isLoading ? (
                    <Locale bn="আপডেট হচ্ছে...">Updating...</Locale>
                  ) : (
                    <Locale bn="আপডেট">Update</Locale>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
