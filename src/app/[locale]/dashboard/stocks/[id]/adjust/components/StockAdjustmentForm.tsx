"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Textarea } from "@udoy/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@udoy/components/ui/form";
import { Stock, QuantityUnit, StockAdjustmentReason } from "@prisma/client";
import { adjustStock } from "../../../actions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { withError } from "@udoy/utils/app-error";
import { Badge } from "@udoy/components/ui/badge";
import { StockManagementUtil } from "@udoy/utils/stock-management";

const stockAdjustmentSchema = z.object({
  quantity: z.coerce.number().refine((val) => val !== 0, {
    message: "Quantity cannot be zero",
  }),
  reason: z.nativeEnum(StockAdjustmentReason),
  note: z.string().optional(),
});

type StockAdjustmentValues = z.infer<typeof stockAdjustmentSchema>;

interface StockAdjustmentFormProps {
  stock: Stock & {
    unit: QuantityUnit;
  };
}

const reasonLabels: Record<StockAdjustmentReason, string> = {
  [StockAdjustmentReason.PURCHASE]: "Purchase/Restock",
  [StockAdjustmentReason.SALE]: "Sale/Consumption",
  [StockAdjustmentReason.DAMAGE]: "Damage/Loss",
  [StockAdjustmentReason.RETURN]: "Return/Refund",
  [StockAdjustmentReason.COUNT_ADJUSTMENT]: "Count Adjustment",
  [StockAdjustmentReason.INITIAL]: "Initial Stock",
};

const reasonDescriptions: Record<StockAdjustmentReason, string> = {
  [StockAdjustmentReason.PURCHASE]: "Increase stock due to new purchases or restocking",
  [StockAdjustmentReason.SALE]: "Decrease stock due to sales or consumption",
  [StockAdjustmentReason.DAMAGE]: "Decrease stock due to damage, expiry, or loss",
  [StockAdjustmentReason.RETURN]: "Increase stock due to customer returns",
  [StockAdjustmentReason.COUNT_ADJUSTMENT]: "Adjust stock based on physical count",
  [StockAdjustmentReason.INITIAL]: "Initial stock entry",
};

export function StockAdjustmentForm({ stock }: StockAdjustmentFormProps) {
  const router = useRouter();
  
  const form = useForm<StockAdjustmentValues>({
    resolver: zodResolver(stockAdjustmentSchema),
    defaultValues: {
      quantity: 0,
      reason: StockAdjustmentReason.PURCHASE,
      note: "",
    },
  });

  const watchedQuantity = form.watch("quantity");
  const watchedReason = form.watch("reason");
  
  const newQuantity = stock.currentQuantity + (watchedQuantity || 0);
  const isDecrease = (watchedQuantity || 0) < 0;
  const isIncrease = (watchedQuantity || 0) > 0;

  const onSubmit = async (data: StockAdjustmentValues) => {
    const result = await withError(() => 
      adjustStock({
        stockId: stock.id,
        quantity: data.quantity,
        reason: data.reason,
        note: data.note,
      })
    );
    
    if (result.success) {
      toast.success("Stock adjusted successfully");
      router.push(`/dashboard/stocks/${stock.id}`);
    } else {
      toast.error(result.error);
    }
  };

  const formattedCurrentQuantity = StockManagementUtil.formatStockQuantity(stock);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Stock Adjustment</CardTitle>
        <CardDescription>
          Adjust stock levels for {stock.name}. Current stock: {formattedCurrentQuantity}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Current Stock Display */}
            <div className="rounded-lg border p-4 bg-muted/50">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Current Stock</h3>
                  <p className="text-2xl font-bold">{formattedCurrentQuantity}</p>
                </div>
                <div className="text-right">
                  <h3 className="font-medium">After Adjustment</h3>
                  <p className={`text-2xl font-bold ${
                    newQuantity < 0 ? 'text-red-600' : 
                    newQuantity <= stock.minimumLevel ? 'text-yellow-600' : 
                    'text-green-600'
                  }`}>
                    {newQuantity.toFixed(2)} {stock.unit.slug}
                  </p>
                  {newQuantity < 0 && (
                    <Badge variant="destructive" className="mt-1">
                      Invalid: Below Zero
                    </Badge>
                  )}
                  {newQuantity >= 0 && newQuantity <= stock.minimumLevel && (
                    <Badge variant="secondary" className="mt-1">
                      Low Stock Warning
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Adjustment Reason</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a reason" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(reasonLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {reasonDescriptions[watchedReason]}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantity Change</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Enter quantity (+ to add, - to subtract)"
                        {...field}
                        className={
                          isDecrease ? 'border-red-300' : 
                          isIncrease ? 'border-green-300' : ''
                        }
                      />
                      <span className="text-sm text-muted-foreground min-w-fit">
                        {stock.unit.slug}
                      </span>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Use positive numbers to increase stock, negative numbers to decrease.
                    {isDecrease && (
                      <span className="text-red-600 block">
                        This will decrease stock by {Math.abs(watchedQuantity)} {stock.unit.slug}
                      </span>
                    )}
                    {isIncrease && (
                      <span className="text-green-600 block">
                        This will increase stock by {watchedQuantity} {stock.unit.slug}
                      </span>
                    )}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="note"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Note (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes about this adjustment..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide context or details about this stock adjustment
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={form.formState.isSubmitting || newQuantity < 0}
              >
                {form.formState.isSubmitting ? "Adjusting..." : "Adjust Stock"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
