"use client";

import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Badge } from "@udoy/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@udoy/components/ui/form";
import { Plus, Unlink, Search } from "lucide-react";
import { Stock, QuantityUnit, ProductStock, Product, ProductImage } from "@prisma/client";
import { linkProductToStock, unlinkProductFromStock } from "../../../actions";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { StockManagementUtil } from "@udoy/utils/stock-management";

type StockWithProducts = Stock & {
  unit: QuantityUnit;
  products: (ProductStock & {
    product: Product & {
      unit: QuantityUnit;
      images: ProductImage[];
    };
  })[];
};

type ProductWithDetails = Product & {
  unit: QuantityUnit;
  images: ProductImage[];
  productStocks: (ProductStock & {
    stock: Stock;
  })[];
};

interface ProductLinkManagerProps {
  stock: StockWithProducts;
  availableProducts: ProductWithDetails[];
}

const linkFormSchema = z.object({
  consumptionRate: z.coerce.number().min(0.01, "Consumption rate must be greater than 0"),
});

type LinkFormValues = z.infer<typeof linkFormSchema>;

export function ProductLinkManager({ stock, availableProducts }: ProductLinkManagerProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProduct, setSelectedProduct] = useState<ProductWithDetails | null>(null);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);

  const form = useForm<LinkFormValues>({
    resolver: zodResolver(linkFormSchema),
    defaultValues: {
      consumptionRate: 1,
    },
  });

  // Filter products based on search and exclude already linked ones
  const linkedProductIds = new Set(stock.products.map(p => p.product.id));
  const filteredProducts = availableProducts.filter(product => 
    !linkedProductIds.has(product.id) &&
    (product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
     product.nam.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleLinkProduct = async (data: LinkFormValues) => {
    if (!selectedProduct) return;

    const result = await withError(() =>
      linkProductToStock({
        productId: selectedProduct.id,
        stockId: stock.id,
        consumptionRate: data.consumptionRate,
      })
    );

    if (result.success) {
      toast.success("Product linked successfully");
      setIsLinkDialogOpen(false);
      setSelectedProduct(null);
      form.reset();
      // Refresh the page to show updated data
      window.location.reload();
    } else {
      toast.error(result.error);
    }
  };

  const handleUnlinkProduct = async (productId: string) => {
    const result = await withError(() =>
      unlinkProductFromStock(productId, stock.id)
    );

    if (result.success) {
      toast.success("Product unlinked successfully");
      // Refresh the page to show updated data
      window.location.reload();
    } else {
      toast.error(result.error);
    }
  };

  const openLinkDialog = (product: ProductWithDetails) => {
    setSelectedProduct(product);
    
    // Calculate suggested consumption rate based on unit conversion
    const suggestedRate = StockManagementUtil.calculateConsumptionRate(
      product.unit.slug,
      stock.unit.slug,
      product.quantity
    );
    
    form.setValue("consumptionRate", suggestedRate);
    setIsLinkDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Currently Linked Products */}
      <Card>
        <CardHeader>
          <CardTitle>Currently Linked Products ({stock.products.length})</CardTitle>
          <CardDescription>
            Products that consume from this stock
          </CardDescription>
        </CardHeader>
        <CardContent>
          {stock.products.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No products linked yet</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Product Unit</TableHead>
                  <TableHead className="text-right">Consumption Rate</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stock.products.map((productStock) => (
                  <TableRow key={productStock.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {productStock.product.images[0] && (
                          <img
                            src={productStock.product.images[0].url}
                            alt={productStock.product.name}
                            className="h-10 w-10 rounded object-cover"
                          />
                        )}
                        <div>
                          <div className="font-medium">
                            {productStock.product.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {productStock.product.nam}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {productStock.product.unit.slug}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      {productStock.consumptionRate} {stock.unit.slug}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUnlinkProduct(productStock.product.id)}
                      >
                        <Unlink className="mr-2 h-4 w-4" />
                        Unlink
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Available Products to Link */}
      <Card>
        <CardHeader>
          <CardTitle>Available Products to Link</CardTitle>
          <CardDescription>
            Compatible products that can be linked to this stock
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="flex items-center gap-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {filteredProducts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                {searchTerm ? "No products found matching your search" : "No compatible products available to link"}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Current Links</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {product.images[0] && (
                          <img
                            src={product.images[0].url}
                            alt={product.name}
                            className="h-10 w-10 rounded object-cover"
                          />
                        )}
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {product.nam}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{product.unit.slug}</Badge>
                    </TableCell>
                    <TableCell>
                      {product.productStocks.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {product.productStocks.map((ps) => (
                            <Badge key={ps.stock.id} variant="secondary" className="text-xs">
                              {ps.stock.name}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">None</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openLinkDialog(product)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Link
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Link Product Dialog */}
      <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Link Product to Stock</DialogTitle>
            <DialogDescription>
              Configure how much stock is consumed per product unit
            </DialogDescription>
          </DialogHeader>

          {selectedProduct && (
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                {selectedProduct.images[0] && (
                  <img
                    src={selectedProduct.images[0].url}
                    alt={selectedProduct.name}
                    className="h-12 w-12 rounded object-cover"
                  />
                )}
                <div>
                  <div className="font-medium">{selectedProduct.name}</div>
                  <div className="text-sm text-muted-foreground">
                    Unit: {selectedProduct.unit.slug} → Stock: {stock.unit.slug}
                  </div>
                </div>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleLinkProduct)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="consumptionRate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Consumption Rate</FormLabel>
                        <FormControl>
                          <div className="flex items-center gap-2">
                            <Input
                              type="number"
                              step="0.01"
                              min="0.01"
                              {...field}
                            />
                            <span className="text-sm text-muted-foreground">
                              {stock.unit.slug} per {selectedProduct.unit.slug}
                            </span>
                          </div>
                        </FormControl>
                        <FormDescription>
                          How much stock (in {stock.unit.slug}) is consumed per product unit ({selectedProduct.unit.slug})
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsLinkDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={form.formState.isSubmitting}>
                      {form.formState.isSubmitting ? "Linking..." : "Link Product"}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
