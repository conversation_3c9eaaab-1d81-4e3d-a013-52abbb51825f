import { notFound } from "next/navigation";
import { Button } from "@udoy/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { getPrisma } from "@udoy/utils/db-utils";
import { ProductLinkManager } from "./components/ProductLinkManager";
import { StockManagementUtil } from "@udoy/utils/stock-management";

interface LinkProductsPageProps {
  params: Promise<{ id: string }>;
}

export default async function LinkProductsPage({ params }: LinkProductsPageProps) {
  const { id } = await params;
  const prisma = getPrisma();

  const [stock, availableProducts] = await Promise.all([
    prisma.stock.findUnique({
      where: { id },
      include: {
        unit: true,
        products: {
          include: {
            product: {
              include: {
                unit: true,
                images: true,
              },
            },
          },
        },
      },
    }),
    // Get products that are compatible with this stock's unit
    prisma.product.findMany({
      include: {
        unit: true,
        images: true,
        productStocks: {
          include: {
            stock: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    }),
  ]);

  if (!stock) {
    notFound();
  }

  // Filter products that are compatible with the stock unit
  const compatibleProducts = availableProducts.filter((product) =>
    StockManagementUtil.areUnitsCompatible(product.unit.slug, stock.unit.slug)
  );

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/stocks/${stock.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Stock
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Manage Product Links</h1>
          <p className="text-muted-foreground">
            Link products to {stock.name} ({stock.unit.slug})
          </p>
        </div>
      </div>

      <ProductLinkManager 
        stock={stock} 
        availableProducts={compatibleProducts}
      />
    </div>
  );
}
