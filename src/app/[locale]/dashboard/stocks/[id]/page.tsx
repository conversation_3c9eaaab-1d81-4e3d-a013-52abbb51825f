import { notFound } from "next/navigation";
import { But<PERSON> } from "@udoy/components/ui/button";
import { ArrowLeft, Edit, TrendingUp, Package, AlertTriangle } from "lucide-react";
import Link from "next/link";
import { getPrisma } from "@udoy/utils/db-utils";
import { Badge } from "@udoy/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { StockManagementUtil } from "@udoy/utils/stock-management";

interface StockDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function StockDetailPage({ params }: StockDetailPageProps) {
  const { id } = await params;
  const prisma = getPrisma();

  const stock = await prisma.stock.findUnique({
    where: { id },
    include: {
      unit: true,
      products: {
        include: {
          product: {
            include: {
              unit: true,
              images: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      },
      stockAdjustments: {
        include: {
          user: {
            select: { name: true },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 10,
      },
    },
  });

  if (!stock) {
    notFound();
  }

  const status = StockManagementUtil.getStockStatus(stock);
  const formattedQuantity = StockManagementUtil.formatStockQuantity(stock);

  const getStatusBadge = () => {
    switch (status) {
      case "OUT_OF_STOCK":
        return <Badge variant="destructive">Out of Stock</Badge>;
      case "LOW_STOCK":
        return <Badge variant="secondary">Low Stock</Badge>;
      case "IN_STOCK":
        return <Badge variant="default">In Stock</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case "OUT_OF_STOCK":
        return <AlertTriangle className="h-5 w-5 text-destructive" />;
      case "LOW_STOCK":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case "IN_STOCK":
        return <Package className="h-5 w-5 text-green-500" />;
      default:
        return <Package className="h-5 w-5 text-muted-foreground" />;
    }
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/stocks">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Stocks
          </Link>
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <h1 className="text-3xl font-bold tracking-tight">{stock.name}</h1>
            {getStatusBadge()}
          </div>
          <p className="text-muted-foreground">
            Stock details and management
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href={`/dashboard/stocks/${stock.id}/adjust`}>
              <TrendingUp className="mr-2 h-4 w-4" />
              Adjust Stock
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/dashboard/stocks/${stock.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      {/* Stock Overview */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Stock</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formattedQuantity}</div>
            <p className="text-xs text-muted-foreground">
              Unit: {stock.unit.slug}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Minimum Level</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stock.minimumLevel} {stock.unit.slug}
            </div>
            <p className="text-xs text-muted-foreground">
              Alert threshold
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Linked Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stock.products.length}</div>
            <p className="text-xs text-muted-foreground">
              Products using this stock
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Linked Products */}
      <Card>
        <CardHeader>
          <CardTitle>Linked Products</CardTitle>
          <CardDescription>
            Products that consume from this stock
          </CardDescription>
        </CardHeader>
        <CardContent>
          {stock.products.length === 0 ? (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No products linked</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Link products to this stock to track consumption
              </p>
              <Button asChild className="mt-4">
                <Link href={`/dashboard/stocks/${stock.id}/link-products`}>
                  Link Products
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-end">
                <Button asChild variant="outline">
                  <Link href={`/dashboard/stocks/${stock.id}/link-products`}>
                    Manage Product Links
                  </Link>
                </Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>Product Unit</TableHead>
                    <TableHead className="text-right">Consumption Rate</TableHead>
                    <TableHead className="text-center">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {stock.products.map((productStock) => (
                    <TableRow key={productStock.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          {productStock.product.images[0] && (
                            <img
                              src={productStock.product.images[0].url}
                              alt={productStock.product.name}
                              className="h-10 w-10 rounded object-cover"
                            />
                          )}
                          <div>
                            <div className="font-medium">
                              {productStock.product.name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {productStock.product.nam}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {productStock.product.unit.slug}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {productStock.consumptionRate} {stock.unit.slug}
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge variant="secondary">Active</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Adjustments */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Adjustments</CardTitle>
          <CardDescription>
            Latest stock adjustments and changes
          </CardDescription>
        </CardHeader>
        <CardContent>
          {stock.stockAdjustments.length === 0 ? (
            <div className="text-center py-8">
              <TrendingUp className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No adjustments yet</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Stock adjustments will appear here
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead className="text-right">Quantity Change</TableHead>
                  <TableHead>Note</TableHead>
                  <TableHead>User</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stock.stockAdjustments.map((adjustment) => (
                  <TableRow key={adjustment.id}>
                    <TableCell>
                      {new Date(adjustment.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {adjustment.reason.replace(/_/g, " ")}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      <span
                        className={
                          adjustment.quantity >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }
                      >
                        {adjustment.quantity >= 0 ? "+" : ""}
                        {adjustment.quantity} {stock.unit.slug}
                      </span>
                    </TableCell>
                    <TableCell className="max-w-xs truncate">
                      {adjustment.note || "-"}
                    </TableCell>
                    <TableCell>{adjustment.user.name}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
