"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@udoy/components/ui/form";
import { Stock, QuantityUnit } from "@prisma/client";
import { updateStock } from "../../../actions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { withError } from "@udoy/utils/app-error";
import { Badge } from "@udoy/components/ui/badge";
import { StockManagementUtil } from "@udoy/utils/stock-management";

const stockEditSchema = z.object({
  name: z.string().min(1, "Stock name is required"),
  minimumLevel: z.coerce.number().min(0, "Minimum level must be non-negative"),
});

type StockEditValues = z.infer<typeof stockEditSchema>;

interface StockEditFormProps {
  stock: Stock & {
    unit: QuantityUnit;
  };
}

export function StockEditForm({ stock }: StockEditFormProps) {
  const router = useRouter();
  
  const form = useForm<StockEditValues>({
    resolver: zodResolver(stockEditSchema),
    defaultValues: {
      name: stock.name,
      minimumLevel: stock.minimumLevel,
    },
  });

  const onSubmit = async (data: StockEditValues) => {
    const result = await withError(() => 
      updateStock(stock.id, data)
    );
    
    if (result.success) {
      toast.success("Stock updated successfully");
      router.push(`/dashboard/stocks/${stock.id}`);
    } else {
      toast.error(result.error);
    }
  };

  const formattedCurrentQuantity = StockManagementUtil.formatStockQuantity(stock);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Edit Stock Information</CardTitle>
        <CardDescription>
          Update the basic information for this stock entry
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Current Stock Info */}
        <div className="rounded-lg border p-4 bg-muted/50 mb-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-sm text-muted-foreground">Current Stock</h3>
              <p className="text-lg font-semibold">{formattedCurrentQuantity}</p>
            </div>
            <div>
              <h3 className="font-medium text-sm text-muted-foreground">Unit</h3>
              <Badge variant="outline">{stock.unit.slug}</Badge>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Note: Stock quantity and unit cannot be changed here. Use stock adjustments to modify quantities.
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stock Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Rice Stock, Cooking Oil Stock"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    A descriptive name for this stock entry
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="minimumLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum Level</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0"
                        {...field}
                      />
                      <span className="text-sm text-muted-foreground">
                        {stock.unit.slug}
                      </span>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Alert when stock falls below this level
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? "Updating..." : "Update Stock"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
