import { notFound } from "next/navigation";
import { But<PERSON> } from "@udoy/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { getPrisma } from "@udoy/utils/db-utils";
import { StockEditForm } from "./components/StockEditForm";

interface StockEditPageProps {
  params: Promise<{ id: string }>;
}

export default async function StockEditPage({ params }: StockEditPageProps) {
  const { id } = await params;
  const prisma = getPrisma();

  const stock = await prisma.stock.findUnique({
    where: { id },
    include: {
      unit: true,
    },
  });

  if (!stock) {
    notFound();
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/stocks/${stock.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Stock
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Stock</h1>
          <p className="text-muted-foreground">
            Update stock information for {stock.name}
          </p>
        </div>
      </div>

      <div className="max-w-2xl">
        <StockEditForm stock={stock} />
      </div>
    </div>
  );
}
