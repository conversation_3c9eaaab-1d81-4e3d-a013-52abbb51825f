"use client";

import Link from "next/link";
import { Badge } from "@udoy/components/ui/badge";
import { Button } from "@udoy/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Edit, TrendingUp, TrendingDown } from "lucide-react";
import { Stock, QuantityUnit, ProductStock, Product } from "@prisma/client";
import { StockManagementUtil } from "@udoy/utils/stock-management";

type StockWithRelations = Stock & {
  unit: QuantityUnit;
  products: (ProductStock & {
    product: Product & {
      unit: QuantityUnit;
    };
  })[];
  _count: {
    products: number;
    stockAdjustments: number;
  };
};

interface StocksTableProps {
  stocks: StockWithRelations[];
}

export function StocksTable({ stocks }: StocksTableProps) {
  const getStatusBadge = (stock: Stock) => {
    const status = StockManagementUtil.getStockStatus(stock);
    
    switch (status) {
      case "OUT_OF_STOCK":
        return <Badge variant="destructive">Out of Stock</Badge>;
      case "LOW_STOCK":
        return <Badge variant="secondary">Low Stock</Badge>;
      case "IN_STOCK":
        return <Badge variant="default">In Stock</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatQuantity = (stock: StockWithRelations) => {
    return StockManagementUtil.formatStockQuantity(stock);
  };

  if (stocks.length === 0) {
    return (
      <div className="rounded-md border">
        <div className="p-8 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-muted">
            <TrendingUp className="h-6 w-6 text-muted-foreground" />
          </div>
          <h3 className="mt-4 text-lg font-semibold">No stocks found</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            Get started by creating your first stock entry.
          </p>
          <Button asChild className="mt-4">
            <Link href="/dashboard/stocks/new">Add Stock</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Stock Name</TableHead>
            <TableHead>Unit</TableHead>
            <TableHead className="text-right">Current Quantity</TableHead>
            <TableHead className="text-right">Minimum Level</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-center">Products</TableHead>
            <TableHead className="text-center">Adjustments</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {stocks.map((stock) => (
            <TableRow key={stock.id}>
              <TableCell>
                <div className="flex flex-col">
                  <span className="font-medium">{stock.name}</span>
                  <span className="text-sm text-muted-foreground">
                    Created {new Date(stock.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </TableCell>
              
              <TableCell>
                <Badge variant="outline">{stock.unit.slug}</Badge>
              </TableCell>
              
              <TableCell className="text-right font-mono">
                {formatQuantity(stock)}
              </TableCell>
              
              <TableCell className="text-right font-mono">
                {stock.minimumLevel} {stock.unit.slug}
              </TableCell>
              
              <TableCell>
                {getStatusBadge(stock)}
              </TableCell>
              
              <TableCell className="text-center">
                <Badge variant="secondary">
                  {stock._count.products}
                </Badge>
              </TableCell>
              
              <TableCell className="text-center">
                <Badge variant="outline">
                  {stock._count.stockAdjustments}
                </Badge>
              </TableCell>
              
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/dashboard/stocks/${stock.id}`}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/dashboard/stocks/${stock.id}/adjust`}>
                        <TrendingUp className="mr-2 h-4 w-4" />
                        Adjust Stock
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/dashboard/stocks/${stock.id}/edit`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Stock
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
