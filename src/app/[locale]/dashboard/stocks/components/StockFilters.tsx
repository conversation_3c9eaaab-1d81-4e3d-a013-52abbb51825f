"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@udoy/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Button } from "@udoy/components/ui/button";
import { X } from "lucide-react";
import { QuantityUnit } from "@prisma/client";

interface StockFiltersProps {
  searchParams: {
    search?: string;
    status?: string;
    unit?: string;
  };
  units: QuantityUnit[];
}

export function StockFilters({ searchParams, units }: StockFiltersProps) {
  const router = useRouter();
  const currentSearchParams = useSearchParams();

  const updateSearchParams = (key: string, value: string | null) => {
    const params = new URLSearchParams(currentSearchParams.toString());
    
    if (value && value !== "all") {
      params.set(key, value);
    } else {
      params.delete(key);
    }
    
    // Reset to first page when filtering
    params.delete("page");
    
    router.push(`/dashboard/stocks?${params.toString()}`);
  };

  const clearFilters = () => {
    router.push("/dashboard/stocks");
  };

  const hasActiveFilters = searchParams.search || searchParams.status || searchParams.unit;

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center">
      {/* Search */}
      <div className="flex-1 max-w-sm">
        <Input
          placeholder="Search stocks..."
          value={searchParams.search || ""}
          onChange={(e) => updateSearchParams("search", e.target.value)}
          className="w-full"
        />
      </div>

      {/* Status Filter */}
      <Select
        value={searchParams.status || "all"}
        onValueChange={(value) => updateSearchParams("status", value)}
      >
        <SelectTrigger className="w-40">
          <SelectValue placeholder="All Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Status</SelectItem>
          <SelectItem value="in_stock">In Stock</SelectItem>
          <SelectItem value="low_stock">Low Stock</SelectItem>
          <SelectItem value="out_of_stock">Out of Stock</SelectItem>
        </SelectContent>
      </Select>

      {/* Unit Filter */}
      <Select
        value={searchParams.unit || "all"}
        onValueChange={(value) => updateSearchParams("unit", value)}
      >
        <SelectTrigger className="w-32">
          <SelectValue placeholder="All Units" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Units</SelectItem>
          {units.map((unit) => (
            <SelectItem key={unit.id} value={unit.id}>
              {unit.slug}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Clear Filters */}
      {hasActiveFilters && (
        <Button
          variant="outline"
          size="sm"
          onClick={clearFilters}
          className="flex items-center gap-2"
        >
          <X className="h-4 w-4" />
          Clear
        </Button>
      )}
    </div>
  );
}
