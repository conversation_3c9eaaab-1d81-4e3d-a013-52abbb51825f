import Link from "next/link";
import { Button } from "@udoy/components/ui/button";
import { Plus } from "lucide-react";
import { StocksTable } from "./components/StocksTable";
import { StockFilters } from "./components/StockFilters";
import { getPrisma } from "@udoy/utils/db-utils";
import { Suspense } from "react";
import Loading from "./loading";

interface SearchParams {
  search?: string;
  status?: string;
  unit?: string;
  page?: string;
}

export default async function PageWrapper({
  searchParams,
}: {
  searchParams: Promise<SearchParams>;
}) {
  const params = await searchParams;
  return (
    <Suspense key={JSON.stringify(params)} fallback={<Loading />}>
      <StocksPage searchParams={params} />
    </Suspense>
  );
}

async function StocksPage({ searchParams }: { searchParams: SearchParams }) {
  const prisma = getPrisma();
  
  // Build query based on search params
  const where: any = {};
  
  if (searchParams.search) {
    where.name = {
      contains: searchParams.search,
      mode: 'insensitive',
    };
  }
  
  if (searchParams.unit) {
    where.unitId = searchParams.unit;
  }
  
  if (searchParams.status) {
    switch (searchParams.status) {
      case 'out_of_stock':
        where.currentQuantity = { lte: 0 };
        break;
      case 'low_stock':
        where.AND = [
          { currentQuantity: { gt: 0 } },
          { currentQuantity: { lte: prisma.stock.fields.minimumLevel } }
        ];
        break;
      case 'in_stock':
        where.currentQuantity = { gt: prisma.stock.fields.minimumLevel };
        break;
    }
  }

  // Get stocks with related data
  const [stocks, units] = await Promise.all([
    prisma.stock.findMany({
      where,
      include: {
        unit: true,
        products: {
          include: {
            product: {
              include: {
                unit: true,
              },
            },
          },
        },
        _count: {
          select: {
            products: true,
            stockAdjustments: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.quantityUnit.findMany({
      where: {
        slug: { in: ['KG', 'LTR', 'PCS'] }, // Only base units for stocks
      },
      orderBy: {
        slug: 'asc',
      },
    }),
  ]);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Stock Management</h1>
          <p className="text-muted-foreground">
            Manage your inventory and track stock levels
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button asChild>
            <Link href="/dashboard/stocks/new">
              <Plus className="mr-2 h-4 w-4" />
              Add Stock
            </Link>
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <StockFilters searchParams={searchParams} units={units} />
        <StocksTable stocks={stocks} />
      </div>
    </div>
  );
}
