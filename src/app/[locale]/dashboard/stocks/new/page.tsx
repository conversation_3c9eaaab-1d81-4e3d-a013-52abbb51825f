import { But<PERSON> } from "@udoy/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { StockForm } from "./components/StockForm";
import { getPrisma } from "@udoy/utils/db-utils";

export default async function NewStockPage() {
  const prisma = getPrisma();
  
  // Get base units only (KG, LTR, PCS)
  const units = await prisma.quantityUnit.findMany({
    where: {
      slug: { in: ['KG', 'LTR', 'PCS'] },
    },
    orderBy: {
      slug: 'asc',
    },
  });

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/stocks">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Stocks
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Stock</h1>
          <p className="text-muted-foreground">
            Add a new stock entry to track inventory
          </p>
        </div>
      </div>

      <div className="max-w-2xl">
        <StockForm units={units} />
      </div>
    </div>
  );
}
