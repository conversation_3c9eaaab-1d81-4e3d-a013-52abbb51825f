"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@udoy/components/ui/form";
import { QuantityUnit } from "@prisma/client";
import { createStock } from "../../actions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { withError } from "@udoy/utils/app-error";

const stockFormSchema = z.object({
  name: z.string().min(1, "Stock name is required"),
  unitId: z.string().min(1, "Unit is required"),
  initialQuantity: z.coerce.number().min(0, "Initial quantity must be non-negative"),
  minimumLevel: z.coerce.number().min(0, "Minimum level must be non-negative"),
});

type StockFormValues = z.infer<typeof stockFormSchema>;

interface StockFormProps {
  units: QuantityUnit[];
}

export function StockForm({ units }: StockFormProps) {
  const router = useRouter();
  
  const form = useForm<StockFormValues>({
    resolver: zodResolver(stockFormSchema),
    defaultValues: {
      name: "",
      unitId: "",
      initialQuantity: 0,
      minimumLevel: 0,
    },
  });

  const onSubmit = async (data: StockFormValues) => {
    const result = await withError(() => createStock(data));
    
    if (result.success) {
      toast.success("Stock created successfully");
      router.push(`/dashboard/stocks/${result.data.id}`);
    } else {
      toast.error(result.error);
    }
  };

  const selectedUnit = units.find(unit => unit.id === form.watch("unitId"));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Stock Information</CardTitle>
        <CardDescription>
          Create a new stock entry. Only base units (KG, LTR, PCS) can be used for stocks.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stock Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Rice Stock, Cooking Oil Stock"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    A descriptive name for this stock entry
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="unitId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Unit</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a unit" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {units.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>
                          <div className="flex items-center gap-2">
                            <span>{unit.slug}</span>
                            <span className="text-muted-foreground">
                              ({unit.full})
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Only base units can be used for stock tracking
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="initialQuantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Initial Quantity</FormLabel>
                    <FormControl>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0"
                          {...field}
                        />
                        {selectedUnit && (
                          <span className="text-sm text-muted-foreground">
                            {selectedUnit.slug}
                          </span>
                        )}
                      </div>
                    </FormControl>
                    <FormDescription>
                      Starting quantity for this stock
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="minimumLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Level</FormLabel>
                    <FormControl>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0"
                          {...field}
                        />
                        {selectedUnit && (
                          <span className="text-sm text-muted-foreground">
                            {selectedUnit.slug}
                          </span>
                        )}
                      </div>
                    </FormControl>
                    <FormDescription>
                      Alert when stock falls below this level
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? "Creating..." : "Create Stock"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
