"use server";

import { getPrisma } from "@udoy/utils/db-utils";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { ActionError } from "@udoy/utils/app-error";
import { revalidatePath } from "next/cache";
import { StockAdjustmentReason } from "@prisma/client";
import { StockManagementUtil } from "@udoy/utils/stock-management";

interface CreateStockData {
  name: string;
  unitId: string;
  initialQuantity: number;
  minimumLevel: number;
}

interface AdjustStockData {
  stockId: string;
  quantity: number;
  reason: StockAdjustmentReason;
  note?: string;
}

interface LinkProductToStockData {
  productId: string;
  stockId: string;
  consumptionRate: number;
}

export async function createStock(data: CreateStockData) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    // Validate unit is a base unit
    const unit = await prisma.quantityUnit.findUnique({
      where: { id: data.unitId },
    });

    if (!unit) {
      return ActionError("Invalid unit selected");
    }

    if (!["KG", "LTR", "PCS"].includes(unit.slug)) {
      return ActionError("Only base units (KG, LTR, PCS) can be used for stocks");
    }

    // Check if stock name already exists
    const existingStock = await prisma.stock.findFirst({
      where: {
        name: {
          equals: data.name,
          mode: "insensitive",
        },
      },
    });

    if (existingStock) {
      return ActionError("A stock with this name already exists");
    }

    // Create stock with initial adjustment
    const stock = await prisma.stock.create({
      data: {
        name: data.name,
        unitId: data.unitId,
        currentQuantity: data.initialQuantity,
        minimumLevel: data.minimumLevel,
        stockAdjustments: {
          create: {
            quantity: data.initialQuantity,
            reason: StockAdjustmentReason.INITIAL,
            note: "Initial stock creation",
            userId,
          },
        },
      },
      include: {
        unit: true,
      },
    });

    revalidatePath("/dashboard/stocks");
    return stock;
  } catch (error) {
    console.error("Create stock error:", error);
    return ActionError("Failed to create stock");
  }
}

export async function adjustStock(data: AdjustStockData) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const stock = await prisma.stock.findUnique({
      where: { id: data.stockId },
      include: { unit: true },
    });

    if (!stock) {
      return ActionError("Stock not found");
    }

    // Calculate new quantity
    const newQuantity = stock.currentQuantity + data.quantity;

    if (newQuantity < 0) {
      return ActionError("Insufficient stock. Cannot reduce below zero.");
    }

    // Update stock and create adjustment record
    const updatedStock = await prisma.stock.update({
      where: { id: data.stockId },
      data: {
        currentQuantity: newQuantity,
        stockAdjustments: {
          create: {
            quantity: data.quantity,
            reason: data.reason,
            note: data.note || "",
            userId,
          },
        },
      },
      include: {
        unit: true,
        stockAdjustments: {
          take: 1,
          orderBy: { createdAt: "desc" },
          include: {
            user: {
              select: { name: true },
            },
          },
        },
      },
    });

    revalidatePath("/dashboard/stocks");
    revalidatePath(`/dashboard/stocks/${data.stockId}`);
    return updatedStock;
  } catch (error) {
    console.error("Adjust stock error:", error);
    return ActionError("Failed to adjust stock");
  }
}

export async function linkProductToStock(data: LinkProductToStockData) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    // Validate product and stock exist
    const [product, stock] = await Promise.all([
      prisma.product.findUnique({
        where: { id: data.productId },
        include: { unit: true },
      }),
      prisma.stock.findUnique({
        where: { id: data.stockId },
        include: { unit: true },
      }),
    ]);

    if (!product) {
      return ActionError("Product not found");
    }

    if (!stock) {
      return ActionError("Stock not found");
    }

    // Check unit compatibility
    const isCompatible = StockManagementUtil.areUnitsCompatible(
      product.unit.slug,
      stock.unit.slug
    );

    if (!isCompatible) {
      return ActionError(
        `Product unit (${product.unit.slug}) is not compatible with stock unit (${stock.unit.slug})`
      );
    }

    // Check if product is already linked to this stock
    const existingLink = await prisma.productStock.findUnique({
      where: {
        productId_stockId: {
          productId: data.productId,
          stockId: data.stockId,
        },
      },
    });

    if (existingLink) {
      return ActionError("Product is already linked to this stock");
    }

    // Create the link
    const productStock = await prisma.productStock.create({
      data: {
        productId: data.productId,
        stockId: data.stockId,
        consumptionRate: data.consumptionRate,
      },
      include: {
        product: {
          include: { unit: true },
        },
        stock: {
          include: { unit: true },
        },
      },
    });

    revalidatePath("/dashboard/stocks");
    revalidatePath(`/dashboard/stocks/${data.stockId}`);
    revalidatePath("/dashboard/products");
    return productStock;
  } catch (error) {
    console.error("Link product to stock error:", error);
    return ActionError("Failed to link product to stock");
  }
}

export async function unlinkProductFromStock(productId: string, stockId: string) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    // Check if link exists
    const existingLink = await prisma.productStock.findUnique({
      where: {
        productId_stockId: {
          productId,
          stockId,
        },
      },
    });

    if (!existingLink) {
      return ActionError("Product is not linked to this stock");
    }

    // Remove the link
    await prisma.productStock.delete({
      where: {
        productId_stockId: {
          productId,
          stockId,
        },
      },
    });

    revalidatePath("/dashboard/stocks");
    revalidatePath(`/dashboard/stocks/${stockId}`);
    revalidatePath("/dashboard/products");
    return true;
  } catch (error) {
    console.error("Unlink product from stock error:", error);
    return ActionError("Failed to unlink product from stock");
  }
}

export async function updateStock(stockId: string, data: Partial<CreateStockData>) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const stock = await prisma.stock.findUnique({
      where: { id: stockId },
    });

    if (!stock) {
      return ActionError("Stock not found");
    }

    // If name is being updated, check for duplicates
    if (data.name && data.name !== stock.name) {
      const existingStock = await prisma.stock.findFirst({
        where: {
          name: {
            equals: data.name,
            mode: "insensitive",
          },
          id: { not: stockId },
        },
      });

      if (existingStock) {
        return ActionError("A stock with this name already exists");
      }
    }

    // Update stock
    const updatedStock = await prisma.stock.update({
      where: { id: stockId },
      data: {
        name: data.name,
        minimumLevel: data.minimumLevel,
      },
      include: {
        unit: true,
      },
    });

    revalidatePath("/dashboard/stocks");
    revalidatePath(`/dashboard/stocks/${stockId}`);
    return updatedStock;
  } catch (error) {
    console.error("Update stock error:", error);
    return ActionError("Failed to update stock");
  }
}
